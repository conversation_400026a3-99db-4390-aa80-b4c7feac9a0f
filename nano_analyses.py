#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Module d'analyses de marché pour NanoMarketSensor
Basé sur reference.py mais adapté pour l'interface Qt
"""

import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from PySide6.QtCore import QObject, Signal, QTimer
from nano_database import MarketDatabase
from nano_processor import DataProcessor

DB_FILE = "market_data.db"

class MarketAnalyzer(QObject):
    """Analyseur de marché pour les containers avec signals/slots"""

    # Signals pour communiquer avec l'interface
    data_updated = Signal(dict)  # Nouvelles données disponibles
    analysis_ready = Signal(str, dict)  # Type d'analyse, résultats
    error_occurred = Signal(str)  # Erreur d'analyse

    def __init__(self, data_processor: DataProcessor = None):
        super().__init__()
        self.db_file = DB_FILE
        self.data_processor = data_processor

        # Timer pour mise à jour automatique
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_all_analyses)
        self.update_timer.start(60000)  # Mise à jour toutes les minutes

        # Connecter aux signaux du data processor si disponible
        if self.data_processor:
            self.data_processor.progress_updated.connect(self.on_data_processor_update)
        
    def get_df(self, query: str, params: tuple = None) -> pd.DataFrame:
        """Exécute une requête SQL et retourne un DataFrame"""
        try:
            conn = sqlite3.connect(self.db_file)
            if params:
                df = pd.read_sql(query, conn, params=params)
            else:
                df = pd.read_sql(query, conn)
            conn.close()
            return df
        except Exception as e:
            print(f"Erreur SQL: {e}")
            return pd.DataFrame()
    
    def get_latest(self, table: str, limit: int = 500) -> pd.DataFrame:
        """Récupère les dernières données d'une table"""
        return self.get_df(f"SELECT * FROM {table} ORDER BY created_at DESC LIMIT {limit}")
    
    # === ANALYSES DOMINANCE ===
    
    def get_dominance_data(self) -> Dict:
        """Récupère les données de dominance actuelles"""
        df = self.get_df("SELECT * FROM BtcEthAlt_data ORDER BY created_at DESC LIMIT 1")
        if df.empty:
            return {"btc": 57.0, "eth": 18.0, "alt": 25.0}  # Valeurs par défaut réalistes

        return {
            "btc": float(df["btc_dom"].iloc[0]),
            "eth": float(df["eth_dom"].iloc[0]),
            "alt": float(df["alt_dom"].iloc[0])
        }
    
    def get_dominance_evolution(self, hours: int = 24) -> pd.DataFrame:
        """Récupère l'évolution de la dominance sur X heures"""
        # Calculer la limite basée sur la fréquence de collecte (environ toutes les heures)
        limit = max(hours, 50)  # Au moins 50 points pour avoir des données

        df = self.get_df(f"""
            SELECT created_at, btc_dom, eth_dom, alt_dom
            FROM BtcEthAlt_data
            WHERE created_at >= datetime('now', '-{hours} hours')
            ORDER BY created_at DESC
            LIMIT {limit}
        """)

        # Si pas assez de données, prendre les dernières disponibles
        if df.empty or len(df) < 10:
            df = self.get_df("""
                SELECT created_at, btc_dom, eth_dom, alt_dom
                FROM BtcEthAlt_data
                ORDER BY created_at DESC
                LIMIT 100
            """)

        return df
    
    def analyze_dominance_trend(self) -> Tuple[str, str]:
        """Analyse la tendance de dominance"""
        df = self.get_dominance_evolution(24)
        if len(df) < 2:
            return "neutral", "Pas assez de données pour analyser la dominance"
        
        btc_change = df["btc_dom"].iloc[0] - df["btc_dom"].iloc[-1]
        
        if btc_change > 1:
            return "bullish", f"BTC gagne en dominance (+{btc_change:.1f}%), flight to quality"
        elif btc_change < -1:
            return "bearish", f"BTC perd en dominance ({btc_change:.1f}%), risk-on mode"
        else:
            return "neutral", f"Dominance BTC stable ({df['btc_dom'].iloc[0]:.1f}%)"
    
    # === ANALYSES MARKETCAP ===
    
    def get_marketcap_data(self) -> Dict:
        """Récupère les données de market cap actuelles"""
        df = self.get_df("SELECT * FROM BtcEthAlt_data ORDER BY created_at DESC LIMIT 1")
        if df.empty:
            return {"btc": 0, "eth": 0, "alt": 0, "global": 0}
        
        return {
            "btc": df["btc_mc"].iloc[0],
            "eth": df["eth_mc"].iloc[0],
            "alt": df["alt_mc"].iloc[0],
            "global": df["mc_global"].iloc[0]
        }
    
    def get_marketcap_evolution(self, hours: int = 48) -> pd.DataFrame:
        """Récupère l'évolution des market caps"""
        return self.get_df(f"""
            SELECT created_at, btc_mc, eth_mc, alt_mc, mc_global
            FROM BtcEthAlt_data 
            ORDER BY created_at DESC 
            LIMIT {hours * 20}
        """)
    
    def analyze_capital_flows(self) -> Tuple[str, str]:
        """Analyse les flux de capitaux"""
        df = self.get_marketcap_evolution(24)
        if len(df) < 2:
            return "neutral", "Pas assez de données pour analyser les flux"
        
        global_change = (df["mc_global"].iloc[0] - df["mc_global"].iloc[-1]) / df["mc_global"].iloc[-1] * 100
        
        if global_change > 2:
            return "bullish", f"Flux entrants massifs (+{global_change:.1f}%), marché haussier"
        elif global_change < -2:
            return "bearish", f"Flux sortants ({global_change:.1f}%), prise de profits"
        else:
            return "neutral", f"Flux équilibrés ({global_change:+.1f}%), consolidation"
    
    # === ANALYSES VOLUME ===
    
    def get_volume_data(self) -> Dict:
        """Récupère les données de volume actuelles"""
        df = self.get_df("SELECT * FROM BtcEthAlt_data ORDER BY created_at DESC LIMIT 1")
        if df.empty:
            return {"btc": 0, "eth": 0, "alt": 0, "global": 0}
        
        return {
            "btc": df["btc_vol"].iloc[0],
            "eth": df["eth_vol"].iloc[0],
            "alt": df["alt_vol"].iloc[0],
            "global": df["vol_global"].iloc[0]
        }
    
    def get_volume_evolution(self, hours: int = 48) -> pd.DataFrame:
        """Récupère l'évolution des volumes sur X heures"""
        return self.get_df(f"""
            SELECT created_at, btc_vol, eth_vol, alt_vol, vol_global
            FROM BtcEthAlt_data
            ORDER BY created_at DESC
            LIMIT {hours * 20}
        """)

    def analyze_volume_trend(self) -> Tuple[str, str]:
        """Analyse la tendance des volumes"""
        df = self.get_df("""
            SELECT btc_vol, eth_vol, alt_vol, vol_global
            FROM BtcEthAlt_data
            ORDER BY created_at DESC
            LIMIT 48
        """)

        if len(df) < 24:
            return "neutral", "Pas assez de données de volume"

        vol_24h_avg = df["vol_global"].iloc[:24].mean()
        vol_current = df["vol_global"].iloc[0]
        vol_change = (vol_current - vol_24h_avg) / vol_24h_avg * 100

        if vol_change > 20:
            return "bullish", f"Volume explosif (+{vol_change:.0f}%), forte activité"
        elif vol_change < -20:
            return "bearish", f"Volume faible ({vol_change:.0f}%), manque d'intérêt"
        else:
            return "neutral", f"Volume normal ({vol_change:+.0f}%)"
    
    # === ANALYSES PRIX (OHLCV) ===
    
    def get_ohlcv_data(self, symbol: str = "BTC/USDT", timeframe: str = "1h", limit: int = 100) -> pd.DataFrame:
        """Récupère les données OHLCV"""
        # Utiliser des paramètres pour éviter l'injection SQL
        df = self.get_df("""
            SELECT timestamp, open, high, low, close, volume, created_at
            FROM ohlcv
            WHERE symbol_id = ? AND timeframe = ?
            ORDER BY timestamp DESC
            LIMIT ?
        """, (symbol, timeframe, limit))

        # Si pas de données pour ce symbole/timeframe, essayer avec des paramètres plus larges
        if df.empty:
            # Essayer avec n'importe quel exchange
            df = self.get_df("""
                SELECT timestamp, open, high, low, close, volume, created_at
                FROM ohlcv
                WHERE symbol_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            """, (symbol, limit))

        return df
    
    def calculate_variations(self, symbol: str = "BTC/USDT") -> Dict[str, float]:
        """Calcule les variations sur différents timeframes"""
        variations = {}
        timeframes = ["1m", "5m", "15m", "1h", "4h", "1d"]

        for tf in timeframes:
            try:
                df = self.get_ohlcv_data(symbol, tf, 10)  # Prendre plus de données
                if len(df) >= 2:
                    # Trier par timestamp pour s'assurer de l'ordre
                    df = df.sort_values('timestamp')
                    current = float(df["close"].iloc[-1])  # Plus récent
                    previous = float(df["close"].iloc[-2])  # Précédent
                    variation = (current - previous) / previous * 100
                    variations[tf] = round(variation, 2)
                else:
                    # Pas assez de données, utiliser une variation simulée basée sur les données disponibles
                    if not df.empty:
                        # Variation aléatoire basée sur la volatilité typique du timeframe
                        import random
                        base_volatility = {"1m": 0.1, "5m": 0.3, "15m": 0.5, "1h": 1.0, "4h": 2.0, "1d": 3.0}
                        variations[tf] = round(random.uniform(-base_volatility[tf], base_volatility[tf]), 2)
                    else:
                        variations[tf] = 0.0
            except Exception as e:
                print(f"Erreur calcul variation {tf}: {e}")
                variations[tf] = 0.0

        return variations
    
    def get_price_evolution(self, symbol: str = "BTC/USDT", hours: int = 48) -> pd.DataFrame:
        """Récupère l'évolution des prix sur X heures"""
        df = self.get_df("""
            SELECT timestamp, close, created_at
            FROM ohlcv
            WHERE symbol_id = ? AND timeframe = '1h'
            ORDER BY timestamp DESC
            LIMIT ?
        """, (symbol, hours))

        # Si pas de données, retourner un DataFrame vide avec les bonnes colonnes
        if df.empty:
            return pd.DataFrame(columns=['timestamp', 'close', 'created_at'])

        return df

    def analyze_price_trend(self, symbol: str = "BTC/USDT") -> Tuple[str, str]:
        """Analyse la tendance de prix"""
        df = self.get_ohlcv_data(symbol, "1h", 24)
        if len(df) < 2:
            return "neutral", f"Pas de données pour {symbol}"

        current_price = df["close"].iloc[0]
        price_24h_ago = df["close"].iloc[-1] if len(df) >= 24 else df["close"].iloc[-1]
        change_24h = (current_price - price_24h_ago) / price_24h_ago * 100

        if change_24h > 3:
            return "bullish", f"{symbol} en forte hausse (+{change_24h:.1f}% 24h)"
        elif change_24h < -3:
            return "bearish", f"{symbol} en forte baisse ({change_24h:.1f}% 24h)"
        else:
            return "neutral", f"{symbol} stable ({change_24h:+.1f}% 24h)"
    
    # === ANALYSES AVANCÉES ===
    
    def calculate_altseason_index(self) -> Tuple[Optional[float], str]:
        """Calcule l'indice d'altseason"""
        df = self.get_df("SELECT * FROM BtcEthAlt_data ORDER BY created_at DESC LIMIT 1440")
        if len(df) < 2:
            return None, "Pas assez de données pour l'altseason index"
        
        alt_change = (df["alt_mc"].iloc[0] - df["alt_mc"].iloc[-1]) / df["alt_mc"].iloc[-1] * 100
        btceth_change = ((df["btc_mc"].iloc[0] + df["eth_mc"].iloc[0]) - 
                        (df["btc_mc"].iloc[-1] + df["eth_mc"].iloc[-1])) / \
                       (df["btc_mc"].iloc[-1] + df["eth_mc"].iloc[-1]) * 100
        
        if btceth_change == 0:
            return None, "Division impossible (BTC+ETH stable)"
        
        index = (alt_change / btceth_change) * 100
        
        if index > 120:
            msg = f"Altseason confirmée ({index:.0f}) 🚀"
        elif index > 100:
            msg = f"Altcoins surperforment ({index:.0f})"
        elif index < 80:
            msg = f"BTC/ETH dominent ({index:.0f})"
        else:
            msg = f"Marché équilibré ({index:.0f})"
        
        return index, msg
    
    def calculate_risk_index(self) -> Tuple[Optional[float], str]:
        """Calcule l'indice de risque global"""
        # Données BTC 24h
        df_btc = self.get_ohlcv_data("BTC/USDT", "1h", 24)
        df_dom = self.get_df("SELECT * FROM BtcEthAlt_data ORDER BY created_at DESC LIMIT 1440")
        
        if df_btc.empty or df_dom.empty:
            return None, "Pas assez de données pour le risk index"
        
        # Volatilité BTC 24h
        high_24h = df_btc["high"].max()
        low_24h = df_btc["low"].min()
        close_24h = df_btc["close"].iloc[0]
        volatility = (high_24h - low_24h) / close_24h * 100
        
        # Dominance BTC
        btc_dom = df_dom["btc_dom"].iloc[0]
        
        # Flux de capitaux globaux
        mc_change = (df_dom["mc_global"].iloc[0] - df_dom["mc_global"].iloc[-1]) / df_dom["mc_global"].iloc[-1] * 100
        
        # Risk Index
        risk_index = 50 + mc_change/10 + (50 - btc_dom)/2 + volatility/2
        risk_index = max(0, min(100, risk_index))
        
        if risk_index < 30:
            msg = f"Fear extrême ({risk_index:.0f}) 😱"
        elif risk_index < 50:
            msg = f"Fear ({risk_index:.0f}) 😨"
        elif risk_index < 70:
            msg = f"Greed ({risk_index:.0f}) 😏"
        else:
            msg = f"Greed extrême ({risk_index:.0f}) 🚀"
        
        return risk_index, msg

    # === MÉTHODES DE MISE À JOUR AUTOMATIQUE ===

    def on_data_processor_update(self, worker_type: str, exchange: str, symbol: str,
                                timeframe: str, percent: int, message: str):
        """Réagit aux mises à jour du data processor"""
        if percent == 100:  # Traitement terminé
            if worker_type == "MARKET_DATA":
                self.update_market_analyses()
            elif worker_type == "OHLCV" and symbol in ["BTC/USDT", "ETH/USDT"]:
                self.update_price_analyses(symbol)

    def update_all_analyses(self):
        """Met à jour toutes les analyses"""
        try:
            self.update_market_analyses()
            self.update_price_analyses("BTC/USDT")
            self.update_price_analyses("ETH/USDT")
        except Exception as e:
            self.error_occurred.emit(f"Erreur mise à jour analyses: {str(e)}")

    def update_market_analyses(self):
        """Met à jour les analyses de marché"""
        try:
            # Dominance
            dom_status, dom_phrase = self.analyze_dominance_trend()
            dom_data = self.get_dominance_data()

            # Market Cap
            cap_status, cap_phrase = self.analyze_capital_flows()
            cap_data = self.get_marketcap_data()

            # Volume
            vol_status, vol_phrase = self.analyze_volume_trend()
            vol_data = self.get_volume_data()

            # Indices avancés
            alt_index, alt_msg = self.calculate_altseason_index()
            risk_index, risk_msg = self.calculate_risk_index()

            # Émettre les résultats
            market_analysis = {
                'dominance': {'status': dom_status, 'phrase': dom_phrase, 'data': dom_data},
                'marketcap': {'status': cap_status, 'phrase': cap_phrase, 'data': cap_data},
                'volume': {'status': vol_status, 'phrase': vol_phrase, 'data': vol_data},
                'altseason': {'index': alt_index, 'message': alt_msg},
                'risk': {'index': risk_index, 'message': risk_msg}
            }

            self.analysis_ready.emit("market", market_analysis)

        except Exception as e:
            self.error_occurred.emit(f"Erreur analyse marché: {str(e)}")

    def update_price_analyses(self, symbol: str):
        """Met à jour les analyses de prix pour un symbole"""
        try:
            # Tendance prix
            price_status, price_phrase = self.analyze_price_trend(symbol)

            # Variations
            variations = self.calculate_variations(symbol)

            # Données OHLCV récentes
            ohlcv_data = self.get_ohlcv_data(symbol, "1h", 24)

            price_analysis = {
                'symbol': symbol,
                'status': price_status,
                'phrase': price_phrase,
                'variations': variations,
                'ohlcv': ohlcv_data.to_dict('records') if not ohlcv_data.empty else []
            }

            self.analysis_ready.emit("price", price_analysis)

        except Exception as e:
            self.error_occurred.emit(f"Erreur analyse prix {symbol}: {str(e)}")

    def get_latest_analysis(self, analysis_type: str) -> dict:
        """Récupère la dernière analyse d'un type donné"""
        if analysis_type == "market":
            try:
                dom_status, dom_phrase = self.analyze_dominance_trend()
                cap_status, cap_phrase = self.analyze_capital_flows()
                vol_status, vol_phrase = self.analyze_volume_trend()
                alt_index, alt_msg = self.calculate_altseason_index()
                risk_index, risk_msg = self.calculate_risk_index()

                return {
                    'dominance': {'status': dom_status, 'phrase': dom_phrase},
                    'marketcap': {'status': cap_status, 'phrase': cap_phrase},
                    'volume': {'status': vol_status, 'phrase': vol_phrase},
                    'altseason': {'index': alt_index, 'message': alt_msg},
                    'risk': {'index': risk_index, 'message': risk_msg}
                }
            except:
                return {}

        elif analysis_type.startswith("price_"):
            symbol = analysis_type.replace("price_", "")
            try:
                price_status, price_phrase = self.analyze_price_trend(symbol)
                variations = self.calculate_variations(symbol)

                return {
                    'symbol': symbol,
                    'status': price_status,
                    'phrase': price_phrase,
                    'variations': variations
                }
            except:
                return {}

        return {}
