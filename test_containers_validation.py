#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de validation des containers corrigés
Teste que tous les containers affichent correctement leurs graphiques et données
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QHBoxLayout
from PySide6.QtCore import QTimer
from nano_content_widget import ContentWidgetWithNanoContainer, CONTAINER_CONFIGS
from nano_analyses import MarketAnalyzer

class ContainerValidationWindow(QMainWindow):
    """Fenêtre de test pour valider les containers"""
    
    def __init__(self):
        super().__init__()
        self.dark_mode = True
        self.containers = []
        self.current_container_index = 0
        self.setup_ui()
        self.setup_analyzer()
        self.create_test_containers()
        
    def setup_ui(self):
        """Configure l'interface"""
        self.setWindowTitle("🧪 Validation des Containers Corrigés")
        self.setGeometry(100, 100, 800, 600)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        
        # Contrôles
        controls_layout = QHBoxLayout()
        
        # Bouton précédent
        self.prev_btn = QPushButton("⬅️ Précédent")
        self.prev_btn.clicked.connect(self.show_previous_container)
        controls_layout.addWidget(self.prev_btn)
        
        # Label du container actuel
        self.container_label = QLabel("Container 1/12")
        self.container_label.setStyleSheet("font-size: 14px; font-weight: bold; padding: 10px;")
        controls_layout.addWidget(self.container_label)
        
        # Bouton suivant
        self.next_btn = QPushButton("➡️ Suivant")
        self.next_btn.clicked.connect(self.show_next_container)
        controls_layout.addWidget(self.next_btn)
        
        # Bouton test automatique
        self.auto_test_btn = QPushButton("🔄 Test Auto")
        self.auto_test_btn.clicked.connect(self.start_auto_test)
        controls_layout.addWidget(self.auto_test_btn)
        
        # Bouton refresh
        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self.refresh_current_container)
        controls_layout.addWidget(refresh_btn)
        
        main_layout.addLayout(controls_layout)
        
        # Zone d'affichage du container
        self.container_area = QWidget()
        self.container_layout = QVBoxLayout(self.container_area)
        main_layout.addWidget(self.container_area)
        
        # Status
        self.status_label = QLabel("Prêt pour les tests...")
        self.status_label.setStyleSheet("color: green; padding: 5px; background-color: #f0f0f0; border-radius: 3px;")
        main_layout.addWidget(self.status_label)
        
    def setup_analyzer(self):
        """Configure l'analyzer pour les tests"""
        try:
            self.analyzer = MarketAnalyzer()
            self.status_label.setText("✅ Analyzer initialisé")
        except Exception as e:
            self.status_label.setText(f"❌ Erreur analyzer: {e}")
            self.analyzer = None
    
    def create_test_containers(self):
        """Crée tous les containers de test"""
        self.containers = []
        
        for i, config in enumerate(CONTAINER_CONFIGS):
            try:
                container = ContentWidgetWithNanoContainer(
                    dark_mode=self.dark_mode,
                    parent=self,
                    container_config=config
                )
                self.containers.append({
                    'widget': container,
                    'config': config,
                    'index': i + 1,
                    'status': 'created'
                })
                print(f"✅ Container {i+1} créé: {config['title']}")
            except Exception as e:
                print(f"❌ Erreur container {i+1}: {e}")
                self.containers.append({
                    'widget': None,
                    'config': config,
                    'index': i + 1,
                    'status': f'error: {e}'
                })
        
        # Afficher le premier container
        self.show_container(0)
    
    def show_container(self, index):
        """Affiche un container spécifique"""
        if not (0 <= index < len(self.containers)):
            return
            
        # Nettoyer la zone d'affichage
        while self.container_layout.count():
            child = self.container_layout.takeAt(0)
            if child.widget():
                child.widget().setParent(None)
        
        # Afficher le nouveau container
        container_info = self.containers[index]
        self.current_container_index = index
        
        # Mettre à jour le label
        self.container_label.setText(f"Container {index + 1}/{len(self.containers)}: {container_info['config']['title']}")
        
        if container_info['widget']:
            self.container_layout.addWidget(container_info['widget'])
            
            # Tester la mise à jour des données
            self.test_container_update(container_info)
            
            self.status_label.setText(f"✅ Container {index + 1} affiché: {container_info['status']}")
        else:
            error_label = QLabel(f"❌ Erreur: {container_info['status']}")
            error_label.setStyleSheet("color: red; font-size: 16px; padding: 20px;")
            self.container_layout.addWidget(error_label)
            self.status_label.setText(f"❌ Container {index + 1} en erreur")
        
        # Mettre à jour les boutons
        self.prev_btn.setEnabled(index > 0)
        self.next_btn.setEnabled(index < len(self.containers) - 1)
    
    def test_container_update(self, container_info):
        """Teste la mise à jour d'un container"""
        if not container_info['widget'] or not self.analyzer:
            return
            
        try:
            container_info['widget'].update_container_data(self.analyzer)
            container_info['status'] = 'updated successfully'
            print(f"✅ Container {container_info['index']} mis à jour avec succès")
        except Exception as e:
            container_info['status'] = f'update error: {e}'
            print(f"❌ Erreur mise à jour container {container_info['index']}: {e}")
    
    def show_previous_container(self):
        """Affiche le container précédent"""
        if self.current_container_index > 0:
            self.show_container(self.current_container_index - 1)
    
    def show_next_container(self):
        """Affiche le container suivant"""
        if self.current_container_index < len(self.containers) - 1:
            self.show_container(self.current_container_index + 1)
    
    def refresh_current_container(self):
        """Rafraîchit le container actuel"""
        self.show_container(self.current_container_index)
    
    def start_auto_test(self):
        """Démarre un test automatique de tous les containers"""
        self.auto_test_btn.setText("🔄 Test en cours...")
        self.auto_test_btn.setEnabled(False)
        
        # Timer pour passer automatiquement d'un container à l'autre
        self.auto_timer = QTimer()
        self.auto_timer.timeout.connect(self.auto_test_next)
        self.auto_test_index = 0
        self.auto_timer.start(3000)  # 3 secondes par container
        
        self.status_label.setText("🔄 Test automatique démarré...")
    
    def auto_test_next(self):
        """Passe au container suivant dans le test automatique"""
        if self.auto_test_index < len(self.containers):
            self.show_container(self.auto_test_index)
            self.auto_test_index += 1
        else:
            # Test terminé
            self.auto_timer.stop()
            self.auto_test_btn.setText("🔄 Test Auto")
            self.auto_test_btn.setEnabled(True)
            
            # Afficher le résumé
            self.show_test_summary()
    
    def show_test_summary(self):
        """Affiche un résumé des tests"""
        successful = sum(1 for c in self.containers if 'successfully' in c['status'])
        total = len(self.containers)
        
        summary = f"✅ Test terminé: {successful}/{total} containers fonctionnels"
        self.status_label.setText(summary)
        print(f"\n{summary}")
        
        # Détail des erreurs
        for i, container in enumerate(self.containers):
            if 'error' in container['status']:
                print(f"❌ Container {i+1} ({container['config']['title']}): {container['status']}")

def main():
    """Fonction principale"""
    app = QApplication(sys.argv)
    
    print("🧪 VALIDATION DES CONTAINERS CORRIGÉS")
    print("=" * 50)
    print("Ce script teste tous les containers pour vérifier:")
    print("• Création sans erreur")
    print("• Affichage des graphiques")
    print("• Mise à jour des données")
    print("• Gestion des erreurs")
    print()
    
    window = ContainerValidationWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
