#!/usr/bin/env python3
"""
Générateur de graphiques matplotlib intégrés dans le dashboard
"""

# Import PySide6 first (always needed)
from PySide6.QtWidgets import QWidget, QVBoxLayout
from PySide6.QtCore import Qt

# Import dependencies first
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import logging

# Import des modules de données
try:
    from nano_database import MarketDatabase
    from nano_analyses import MarketAnalyzer
    DATABASE_IMPORTS_OK = True
except ImportError as e:
    print(f"Warning: Database imports failed: {e}")
    DATABASE_IMPORTS_OK = False

# Then try matplotlib imports
try:
    # Try to import packaging first to avoid the error
    try:
        import packaging.version
    except ImportError:
        # Install packaging if not available
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "packaging"])
        import packaging.version

    import matplotlib
    matplotlib.use('Qt5Agg')  # Forcer le backend Qt
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    MATPLOTLIB_IMPORTS_OK = True
    print("✅ Matplotlib imports successful")
except ImportError as e:
    print(f"Erreur import matplotlib: {e}")
    MATPLOTLIB_IMPORTS_OK = False
    # Create dummy classes to avoid NameError
    class FigureCanvas:
        pass
    class Figure:
        pass
    class plt:
        @staticmethod
        def style():
            pass
        @staticmethod
        def FuncFormatter(func):
            return func
except Exception as e:
    print(f"Erreur matplotlib: {e}")
    MATPLOTLIB_IMPORTS_OK = False
    # Create dummy classes to avoid NameError
    class FigureCanvas:
        pass
    class Figure:
        pass
    class plt:
        @staticmethod
        def style():
            pass
        @staticmethod
        def FuncFormatter(func):
            return func

class MatplotlibChartGenerator:
    """Générateur de graphiques matplotlib pour intégration dashboard"""

    def __init__(self, dark_mode: bool = True):
        if not MATPLOTLIB_IMPORTS_OK:
            raise ImportError("Matplotlib non disponible")
        self.dark_mode = dark_mode
        self.setup_style()

        # Initialiser les connexions à la base de données
        self.db = None
        self.analyses = None
        if DATABASE_IMPORTS_OK:
            try:
                self.db = MarketDatabase()
                self.analyses = MarketAnalyzer()
            except Exception as e:
                print(f"Warning: Could not initialize database connections: {e}")
        
    def setup_style(self):
        """Configure le style matplotlib selon le mode"""
        if self.dark_mode:
            plt.style.use('dark_background')
            self.bg_color = "#0d111704"
            self.text_color = '#c9d1d9'
            self.grid_color = '#30363d'
            self.line_colors = ['#58a6ff', '#7c3aed', '#f85149', '#56d364', '#ffa657']
        else:
            plt.style.use('default')
            self.bg_color = "#ffffff0a"
            self.text_color = '#24292f'
            self.grid_color = '#d0d7de'
            self.line_colors = ['#0969da', '#8250df', '#cf222e', '#1a7f37', '#fb8500']
    
    def create_figure_widget(self, figsize=(8, 6)) -> QWidget:
        """Crée un widget contenant une figure matplotlib"""
        # Créer la figure
        fig = Figure(figsize=figsize, facecolor=self.bg_color)
        
        # Créer le canvas Qt
        canvas = FigureCanvas(fig)
        
        # Créer le widget container
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        layout.addWidget(canvas)
        
        # Stocker les références
        widget.figure = fig
        widget.canvas = canvas
        
        return widget
    
    def create_line_chart(self, x_data, y_data, title="Line Chart", xlabel="X", ylabel="Y") -> QWidget:
        """Crée un graphique linéaire intégré"""
        widget = self.create_figure_widget()
        fig = widget.figure
        
        # Créer le subplot
        ax = fig.add_subplot(111)
        
        # Données par défaut si vides
        if not x_data or not y_data:
            x_data = list(range(50))
            y_data = [50000 + i*100 + np.random.randn()*500 for i in x_data]
        
        # Tracer la ligne
        ax.plot(x_data, y_data, color=self.line_colors[0], linewidth=2)
        
        # Style
        ax.set_title(title, color=self.text_color, fontsize=10, fontweight='bold')
        ax.set_xlabel(xlabel, color=self.text_color)
        ax.set_ylabel(ylabel, color=self.text_color)
        ax.grid(True, color=self.grid_color, alpha=0.3)
        ax.set_facecolor(self.bg_color)
        
        # Couleurs des axes
        ax.tick_params(colors=self.text_color)
        for spine in ax.spines.values():
            spine.set_color(self.grid_color)
        
        fig.tight_layout()
        widget.canvas.draw()
        
        return widget
    
    def create_candlestick_chart(self, ohlcv_data: pd.DataFrame = None, title="Candlestick Chart", symbol="BTC/USDT") -> QWidget:
        """Crée un graphique en chandelles intégré"""
        widget = self.create_figure_widget()
        fig = widget.figure
        ax = fig.add_subplot(111)

        # Essayer d'abord de récupérer les vraies données
        if ohlcv_data is None or ohlcv_data.empty:
            ohlcv_data = self.get_real_ohlcv_data(symbol=symbol)

        # Données par défaut si toujours vides
        if ohlcv_data is None or ohlcv_data.empty:
            dates = pd.date_range(start='2024-01-01', periods=50, freq='h')
            np.random.seed(42)
            close_prices = 50000 + np.cumsum(np.random.randn(50) * 100)

            ohlcv_data = pd.DataFrame({
                'timestamp': dates,
                'open': close_prices + np.random.randn(50) * 50,
                'high': close_prices + np.abs(np.random.randn(50) * 100),
                'low': close_prices - np.abs(np.random.randn(50) * 100),
                'close': close_prices,
                'volume': np.random.exponential(1000000, 50)
            })
        
        # Simuler des chandelles avec des barres
        for i, row in ohlcv_data.iterrows():
            color = self.line_colors[1] if row['close'] >= row['open'] else self.line_colors[2]
            
            # Corps de la chandelle
            height = abs(row['close'] - row['open'])
            bottom = min(row['open'], row['close'])
            ax.bar(i, height, bottom=bottom, color=color, alpha=0.8, width=0.8)
            
            # Mèches
            ax.plot([i, i], [row['low'], row['high']], color=color, linewidth=1)
        
        ax.set_title(title, color=self.text_color, fontsize=14, fontweight='bold')
        ax.set_xlabel("Time", color=self.text_color)
        ax.set_ylabel("Price", color=self.text_color)
        ax.grid(True, color=self.grid_color, alpha=0.3)
        ax.set_facecolor(self.bg_color)
        
        # Style des axes
        ax.tick_params(colors=self.text_color)
        for spine in ax.spines.values():
            spine.set_color(self.grid_color)
        
        fig.tight_layout()
        widget.canvas.draw()

        return widget

    def get_real_dominance_data(self, hours: int = 48) -> pd.DataFrame:
        """Récupère les vraies données de dominance depuis la base"""
        if not self.db:
            return pd.DataFrame()

        try:
            # Récupérer les données de marché récentes
            df = self.db.get_market_data(limit=hours * 20)  # ~20 points par heure
            if df.empty:
                return pd.DataFrame()

            # Reformater pour les graphiques
            df_formatted = pd.DataFrame({
                'created_at': df.index,
                'btc_dom': df['btc_dom'],
                'eth_dom': df['eth_dom'],
                'alt_dom': df['alt_dom']
            })

            return df_formatted.reset_index(drop=True)
        except Exception as e:
            print(f"Error getting real dominance data: {e}")
            return pd.DataFrame()

    def create_dominance_chart(self, dominance_data: pd.DataFrame = None) -> QWidget:
        """Crée un graphique de dominance BTC/ETH/Alt intégré"""
        widget = self.create_figure_widget()
        fig = widget.figure
        ax = fig.add_subplot(111)

        # Essayer d'abord de récupérer les vraies données
        if dominance_data is None or dominance_data.empty:
            dominance_data = self.get_real_dominance_data()

        # Données par défaut si toujours vides
        if dominance_data is None or dominance_data.empty:
            dates = pd.date_range(start='2024-01-01', periods=100, freq='h')
            btc_dom = 57 + np.random.randn(100) * 2
            eth_dom = 18 + np.random.randn(100) * 1
            alt_dom = 100 - btc_dom - eth_dom

            dominance_data = pd.DataFrame({
                'created_at': dates,
                'btc_dom': btc_dom,
                'eth_dom': eth_dom,
                'alt_dom': alt_dom
            })
        
        # Tracer les lignes de dominance avec vraies dates
        if 'created_at' in dominance_data.columns:
            x_data = pd.to_datetime(dominance_data['created_at'])
        else:
            x_data = pd.date_range(start='2024-01-01', periods=len(dominance_data), freq='h')

        ax.plot(x_data, dominance_data['btc_dom'], color=self.line_colors[0],
                linewidth=2, label='BTC Dominance')
        ax.plot(x_data, dominance_data['eth_dom'], color=self.line_colors[1],
                linewidth=2, label='ETH Dominance')
        ax.plot(x_data, dominance_data['alt_dom'], color=self.line_colors[2],
                linewidth=2, label='ALT Dominance')
        
        ax.set_title("Market Dominance", color=self.text_color, fontsize=14, fontweight='bold')
        ax.set_xlabel("Time", color=self.text_color)
        ax.set_ylabel("Dominance (%)", color=self.text_color)
        ax.grid(True, color=self.grid_color, alpha=0.3)
        ax.set_facecolor(self.bg_color)
        ax.legend(facecolor=self.bg_color, edgecolor=self.grid_color,
                 labelcolor=self.text_color)

        # Formatage des dates sur l'axe X
        if hasattr(x_data, 'dtype') and 'datetime' in str(x_data.dtype):
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            ax.xaxis.set_major_locator(mdates.HourLocator(interval=6))
            fig.autofmt_xdate()
        
        # Style des axes
        ax.tick_params(colors=self.text_color)
        for spine in ax.spines.values():
            spine.set_color(self.grid_color)
        
        fig.tight_layout()
        widget.canvas.draw()
        
        return widget

    def get_real_volume_data(self, hours: int = 48) -> pd.DataFrame:
        """Récupère les vraies données de volume depuis la base"""
        if not self.db:
            return pd.DataFrame()

        try:
            df = self.db.get_market_data(limit=hours * 20)
            if df.empty:
                return pd.DataFrame()

            df_formatted = pd.DataFrame({
                'created_at': df.index,
                'vol_global': df['vol_global']
            })

            return df_formatted.reset_index(drop=True)
        except Exception as e:
            print(f"Error getting real volume data: {e}")
            return pd.DataFrame()

    def get_real_marketcap_data(self, hours: int = 48) -> pd.DataFrame:
        """Récupère les vraies données de market cap depuis la base"""
        if not self.db:
            return pd.DataFrame()

        try:
            df = self.db.get_market_data(limit=hours * 20)
            if df.empty:
                return pd.DataFrame()

            df_formatted = pd.DataFrame({
                'created_at': df.index,
                'mc_global': df['mc_global']
            })

            return df_formatted.reset_index(drop=True)
        except Exception as e:
            print(f"Error getting real marketcap data: {e}")
            return pd.DataFrame()

    def get_real_ohlcv_data(self, symbol: str = "BTC/USDT", timeframe: str = "1h", limit: int = 100) -> pd.DataFrame:
        """Récupère les vraies données OHLCV depuis la base"""
        if not self.db:
            return pd.DataFrame()

        try:
            # Essayer avec différents exchanges
            exchanges = ['binance', 'coinbase', 'kraken']
            for exchange in exchanges:
                df = self.db.get_ohlcv(symbol, exchange, timeframe, limit=limit)
                if not df.empty:
                    # Reformater pour les graphiques
                    df_formatted = pd.DataFrame({
                        'timestamp': df.index,
                        'open': df['open'],
                        'high': df['high'],
                        'low': df['low'],
                        'close': df['close'],
                        'volume': df['volume']
                    })
                    return df_formatted.reset_index(drop=True)

            return pd.DataFrame()
        except Exception as e:
            print(f"Error getting real OHLCV data: {e}")
            return pd.DataFrame()

    def create_volume_chart(self, volume_data: pd.DataFrame = None) -> QWidget:
        """Crée un graphique de volume intégré"""
        widget = self.create_figure_widget()
        fig = widget.figure
        ax = fig.add_subplot(111)

        # Essayer d'abord de récupérer les vraies données
        if volume_data is None or volume_data.empty:
            volume_data = self.get_real_volume_data()

        # Données par défaut si toujours vides
        if volume_data is None or volume_data.empty:
            dates = pd.date_range(start='2024-01-01', periods=50, freq='h')
            volumes = np.random.exponential(50000000000, 50)  # 50B moyenne

            volume_data = pd.DataFrame({
                'created_at': dates,
                'vol_global': volumes
            })
        
        # Graphique en barres avec vraies dates
        if 'created_at' in volume_data.columns:
            x_data = pd.to_datetime(volume_data['created_at'])
        else:
            x_data = pd.date_range(start='2024-01-01', periods=len(volume_data), freq='h')

        ax.bar(x_data, volume_data['vol_global'], color=self.line_colors[3], alpha=0.7)
        
        ax.set_title("Global Volume", color=self.text_color, fontsize=14, fontweight='bold')
        ax.set_xlabel("Time", color=self.text_color)
        ax.set_ylabel("Volume ($)", color=self.text_color)
        ax.grid(True, color=self.grid_color, alpha=0.3)
        ax.set_facecolor(self.bg_color)
        
        # Formatter les valeurs en milliards
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1e9:.1f}B'))
        
        # Style des axes
        ax.tick_params(colors=self.text_color)
        for spine in ax.spines.values():
            spine.set_color(self.grid_color)
        
        fig.tight_layout()
        widget.canvas.draw()
        
        return widget
    
    def create_correlation_heatmap(self, corr_data: pd.DataFrame) -> QWidget:
        """Crée une heatmap de corrélation intégrée"""
        widget = self.create_figure_widget()
        fig = widget.figure
        ax = fig.add_subplot(111)
        
        # Données par défaut si vides ou None
        if corr_data is None or corr_data.empty:
            symbols = ['BTC', 'ETH']
            corr_matrix = np.random.rand(5, 5)
            corr_matrix = (corr_matrix + corr_matrix.T) / 2  # Symétrique
            np.fill_diagonal(corr_matrix, 1)  # Diagonale = 1
            corr_data = pd.DataFrame(corr_matrix, index=symbols, columns=symbols)
        
        # Créer la heatmap
        im = ax.imshow(corr_data.values, cmap='RdYlBu_r', aspect='auto', vmin=-1, vmax=1)
        
        # Labels
        ax.set_xticks(range(len(corr_data.columns)))
        ax.set_yticks(range(len(corr_data.index)))
        ax.set_xticklabels(corr_data.columns, color=self.text_color)
        ax.set_yticklabels(corr_data.index, color=self.text_color)
        
        # Ajouter les valeurs dans les cellules
        for i in range(len(corr_data.index)):
            for j in range(len(corr_data.columns)):
                text = ax.text(j, i, f'{corr_data.iloc[i, j]:.2f}',
                             ha="center", va="center", color=self.text_color)
        
        ax.set_title("Correlation Matrix", color=self.text_color, fontsize=14, fontweight='bold')
        ax.set_facecolor(self.bg_color)
        
        # Colorbar
        cbar = fig.colorbar(im, ax=ax)
        cbar.ax.yaxis.set_tick_params(color=self.text_color)
        cbar.ax.yaxis.set_ticklabels(cbar.ax.yaxis.get_ticklabels(), color=self.text_color)
        
        fig.tight_layout()
        widget.canvas.draw()
        
        return widget
    
    def create_marketcap_chart(self, marketcap_data: pd.DataFrame = None) -> QWidget:
        """Crée un graphique de market cap intégré"""
        widget = self.create_figure_widget()
        fig = widget.figure
        ax = fig.add_subplot(111)

        # Essayer d'abord de récupérer les vraies données
        if marketcap_data is None or marketcap_data.empty:
            marketcap_data = self.get_real_marketcap_data()

        # Données par défaut si toujours vides
        if marketcap_data is None or marketcap_data.empty:
            dates = pd.date_range(start='2024-01-01', periods=100, freq='h')
            mc_values = 3.5e12 + np.cumsum(np.random.randn(100) * 1e10)  # ~3.5T base

            marketcap_data = pd.DataFrame({
                'created_at': dates,
                'mc_global': mc_values
            })

        # Graphique linéaire avec vraies dates
        if 'created_at' in marketcap_data.columns:
            x_data = pd.to_datetime(marketcap_data['created_at'])
        else:
            x_data = pd.date_range(start='2024-01-01', periods=len(marketcap_data), freq='h')

        ax.plot(x_data, marketcap_data['mc_global'], color=self.line_colors[4], linewidth=2)

        ax.set_title("Global Market Cap", color=self.text_color, fontsize=14, fontweight='bold')
        ax.set_xlabel("Time", color=self.text_color)
        ax.set_ylabel("Market Cap ($)", color=self.text_color)
        ax.grid(True, color=self.grid_color, alpha=0.3)
        ax.set_facecolor(self.bg_color)

        # Formatter les valeurs en trillions
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1e12:.2f}T'))

        # Style des axes
        ax.tick_params(colors=self.text_color)
        for spine in ax.spines.values():
            spine.set_color(self.grid_color)

        fig.tight_layout()
        widget.canvas.draw()

        return widget

    def create_altseason_gauge(self, altseason_index, altseason_msg):
        """Crée un gauge d'altseason intégré"""
        widget = self.create_figure_widget(figsize=(6, 4))
        fig = widget.figure
        ax = fig.add_subplot(111)

        # Données par défaut si None
        if altseason_index is None:
            altseason_index = 100
            altseason_msg = "Données non disponibles"

        # Créer un gauge simple avec des barres
        categories = ['BTC/ETH\nDominant', 'Équilibré', 'Altseason']
        values = [max(0, 80-altseason_index), max(0, min(40, altseason_index-80)), max(0, altseason_index-120)]
        colors = [self.line_colors[2], self.line_colors[4], self.line_colors[1]]

        bars = ax.bar(categories, values, color=colors, alpha=0.7)

        # Ajouter l'index actuel
        ax.axhline(y=altseason_index, color=self.text_color, linestyle='--', linewidth=2,
                  label=f'Index: {altseason_index:.0f}')

        ax.set_title("Altseason Index", color=self.text_color, fontsize=14, fontweight='bold')
        ax.set_ylabel("Index Value", color=self.text_color)
        ax.grid(True, color=self.grid_color, alpha=0.3)
        ax.set_facecolor(self.bg_color)
        ax.legend(facecolor=self.bg_color, edgecolor=self.grid_color, labelcolor=self.text_color)

        # Style des axes
        ax.tick_params(colors=self.text_color)
        for spine in ax.spines.values():
            spine.set_color(self.grid_color)

        fig.tight_layout()
        widget.canvas.draw()

        return widget

    def create_risk_gauge(self, risk_index, risk_msg):
        """Crée un gauge de risque intégré"""
        widget = self.create_figure_widget(figsize=(6, 4))
        fig = widget.figure
        ax = fig.add_subplot(111)

        # Données par défaut si None
        if risk_index is None:
            risk_index = 50
            risk_msg = "Données non disponibles"

        # Créer un gauge avec des zones colorées
        zones = ['Fear\nExtrême', 'Fear', 'Greed', 'Greed\nExtrême']
        zone_ranges = [30, 20, 20, 30]  # 0-30, 30-50, 50-70, 70-100
        zone_colors = ['red', 'orange', 'yellow', 'green']

        # Déterminer la zone actuelle
        current_zone = 0
        if risk_index >= 70: current_zone = 3
        elif risk_index >= 50: current_zone = 2
        elif risk_index >= 30: current_zone = 1

        bars = ax.bar(zones, zone_ranges, color=zone_colors, alpha=0.7)
        bars[current_zone].set_alpha(1.0)  # Mettre en évidence la zone actuelle

        # Ajouter l'index actuel
        ax.axhline(y=risk_index, color=self.text_color, linestyle='--', linewidth=2,
                  label=f'Risk Index: {risk_index:.0f}')

        ax.set_title("Global Risk Index", color=self.text_color, fontsize=14, fontweight='bold')
        ax.set_ylabel("Risk Level", color=self.text_color)
        ax.grid(True, color=self.grid_color, alpha=0.3)
        ax.set_facecolor(self.bg_color)
        ax.legend(facecolor=self.bg_color, edgecolor=self.grid_color, labelcolor=self.text_color)

        # Style des axes
        ax.tick_params(colors=self.text_color)
        for spine in ax.spines.values():
            spine.set_color(self.grid_color)

        fig.tight_layout()
        widget.canvas.draw()

        return widget

    def create_capital_flows_chart(self, flows_data: pd.DataFrame) -> QWidget:
        """Crée un graphique de flux de capitaux intégré"""
        widget = self.create_figure_widget()
        fig = widget.figure
        ax = fig.add_subplot(111)

        # Données par défaut si vides ou None
        if flows_data is None or flows_data.empty:
            dates = pd.date_range(start='2024-01-01', periods=50, freq='h')
            btc_flows = np.random.randn(50) * 1e9  # Flux BTC en milliards
            eth_flows = np.random.randn(50) * 0.5e9  # Flux ETH en milliards
            alt_flows = np.random.randn(50) * 0.3e9  # Flux ALT en milliards

            flows_data = pd.DataFrame({
                'created_at': dates,
                'btc_flows': btc_flows,
                'eth_flows': eth_flows,
                'alt_flows': alt_flows
            })

        # Graphique en aires empilées
        if 'created_at' in flows_data.columns:
            x_data = pd.to_datetime(flows_data['created_at'])
        else:
            x_data = pd.date_range(start='2024-01-01', periods=len(flows_data), freq='h')

        # Tracer les flux avec des aires empilées
        ax.fill_between(x_data, 0, flows_data.get('btc_flows', 0),
                       color=self.line_colors[0], alpha=0.7, label='BTC Flows')
        ax.fill_between(x_data, flows_data.get('btc_flows', 0),
                       flows_data.get('btc_flows', 0) + flows_data.get('eth_flows', 0),
                       color=self.line_colors[1], alpha=0.7, label='ETH Flows')
        ax.fill_between(x_data,
                       flows_data.get('btc_flows', 0) + flows_data.get('eth_flows', 0),
                       flows_data.get('btc_flows', 0) + flows_data.get('eth_flows', 0) + flows_data.get('alt_flows', 0),
                       color=self.line_colors[2], alpha=0.7, label='ALT Flows')

        ax.set_title("Capital Flows", color=self.text_color, fontsize=14, fontweight='bold')
        ax.set_xlabel("Time", color=self.text_color)
        ax.set_ylabel("Flows ($)", color=self.text_color)
        ax.grid(True, color=self.grid_color, alpha=0.3)
        ax.set_facecolor(self.bg_color)
        ax.legend(facecolor=self.bg_color, edgecolor=self.grid_color, labelcolor=self.text_color)

        # Formatter les valeurs en milliards
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1e9:.1f}B'))

        # Style des axes
        ax.tick_params(colors=self.text_color)
        for spine in ax.spines.values():
            spine.set_color(self.grid_color)

        fig.tight_layout()
        widget.canvas.draw()

        return widget

    def create_orderbook_chart(self, orderbook_data, symbol="BTC/USDT") -> QWidget:
        """Crée un graphique de profondeur du carnet d'ordres intégré"""
        widget = self.create_figure_widget()
        fig = widget.figure
        ax = fig.add_subplot(111)

        # Données par défaut si vides ou None
        if orderbook_data is None:
            # Simuler des données d'orderbook
            price_center = 50000
            price_range = np.linspace(price_center * 0.95, price_center * 1.05, 100)

            # Bids (ordres d'achat) - prix inférieurs
            bids_prices = price_range[price_range <= price_center]
            bids_volumes = np.exp(-(price_center - bids_prices) / 1000) * np.random.exponential(10, len(bids_prices))

            # Asks (ordres de vente) - prix supérieurs
            asks_prices = price_range[price_range >= price_center]
            asks_volumes = np.exp(-(asks_prices - price_center) / 1000) * np.random.exponential(10, len(asks_prices))

            orderbook_data = {
                'bids': list(zip(bids_prices, bids_volumes)),
                'asks': list(zip(asks_prices, asks_volumes))
            }

        # Tracer les bids et asks
        if 'bids' in orderbook_data and orderbook_data['bids']:
            bids_prices, bids_volumes = zip(*orderbook_data['bids'])
            ax.fill_between(bids_prices, 0, bids_volumes,
                           color=self.line_colors[1], alpha=0.7, label='Bids', step='pre')

        if 'asks' in orderbook_data and orderbook_data['asks']:
            asks_prices, asks_volumes = zip(*orderbook_data['asks'])
            ax.fill_between(asks_prices, 0, asks_volumes,
                           color=self.line_colors[2], alpha=0.7, label='Asks', step='pre')

        ax.set_title(f"Orderbook Depth - {symbol}", color=self.text_color, fontsize=14, fontweight='bold')
        ax.set_xlabel("Price ($)", color=self.text_color)
        ax.set_ylabel("Volume", color=self.text_color)
        ax.grid(True, color=self.grid_color, alpha=0.3)
        ax.set_facecolor(self.bg_color)
        ax.legend(facecolor=self.bg_color, edgecolor=self.grid_color, labelcolor=self.text_color)

        # Style des axes
        ax.tick_params(colors=self.text_color)
        for spine in ax.spines.values():
            spine.set_color(self.grid_color)

        fig.tight_layout()
        widget.canvas.draw()

        return widget

    def create_vol_dominance_scatter(self, vol_dom_data: pd.DataFrame) -> QWidget:
        """Crée un scatter plot volume vs dominance intégré"""
        widget = self.create_figure_widget()
        fig = widget.figure
        ax = fig.add_subplot(111)

        # Données par défaut si vides ou None
        if vol_dom_data is None or vol_dom_data.empty:
            # Simuler des données de corrélation volume/dominance
            n_points = 100
            btc_dom = np.random.normal(57, 5, n_points)  # Dominance BTC autour de 57%
            volume = np.random.exponential(50e9, n_points)  # Volume global

            # Ajouter une corrélation négative faible
            volume = volume - (btc_dom - 57) * 1e9

            vol_dom_data = pd.DataFrame({
                'btc_dom': btc_dom,
                'vol_global': volume,
                'timestamp': pd.date_range(start='2024-01-01', periods=n_points, freq='h')
            })

        # Scatter plot
        scatter = ax.scatter(vol_dom_data.get('vol_global', []), vol_dom_data.get('btc_dom', []),
                           c=self.line_colors[0], alpha=0.6, s=30)

        # Ligne de tendance si assez de données
        if len(vol_dom_data) > 10:
            try:
                x = vol_dom_data.get('vol_global', [])
                y = vol_dom_data.get('btc_dom', [])
                if len(x) > 0 and len(y) > 0:
                    z = np.polyfit(x, y, 1)
                    p = np.poly1d(z)
                    ax.plot(x, p(x), color=self.line_colors[2], linestyle='--', alpha=0.8, label='Trend')
            except:
                pass  # Ignore si erreur de calcul

        ax.set_title("Volume vs BTC Dominance", color=self.text_color, fontsize=14, fontweight='bold')
        ax.set_xlabel("Global Volume ($)", color=self.text_color)
        ax.set_ylabel("BTC Dominance (%)", color=self.text_color)
        ax.grid(True, color=self.grid_color, alpha=0.3)
        ax.set_facecolor(self.bg_color)

        # Formatter l'axe X en milliards
        ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1e9:.0f}B'))

        # Style des axes
        ax.tick_params(colors=self.text_color)
        for spine in ax.spines.values():
            spine.set_color(self.grid_color)

        fig.tight_layout()
        widget.canvas.draw()

        return widget

    def update_dark_mode(self, dark_mode: bool):
        """Met à jour le mode sombre/clair"""
        self.dark_mode = dark_mode
        self.setup_style()
