# 📊 Connexion des Graphiques aux Données Réelles

## Vue d'ensemble

Les fonctions de graphiques dans `nano_matplotlib_charts.py` sont maintenant connectées aux vraies données de la base SQLite. Voici comment cela fonctionne :

## 🔗 Architecture de Connexion

### Modules impliqués :
- **`nano_matplotlib_charts.py`** : Générateur de graphiques matplotlib
- **`nano_database.py`** : Interface avec la base SQLite
- **`nano_analyses.py`** : Analyses et requêtes de données
- **Base SQLite** : `~/.nano_market_sensor/market_data.db`

## 📈 Graphiques Connectés aux Vraies Données

### 1. Graphique de Dominance (`create_dominance_chart`)
```python
# Fonction modifiée pour utiliser les vraies données
def create_dominance_chart(self, dominance_data: pd.DataFrame = None) -> QWidget:
    # Récupère automatiquement les données si non fournies
    if dominance_data is None or dominance_data.empty:
        dominance_data = self.get_real_dominance_data()
```

**Source de données :**
- Table : `BtcEthAlt_data`
- Colonnes : `btc_dom`, `eth_dom`, `alt_dom`, `created_at`
- Méthode : `get_real_dominance_data(hours=48)`

### 2. Graphique de Volume (`create_volume_chart`)
```python
def create_volume_chart(self, volume_data: pd.DataFrame = None) -> QWidget:
    if volume_data is None or volume_data.empty:
        volume_data = self.get_real_volume_data()
```

**Source de données :**
- Table : `BtcEthAlt_data`
- Colonnes : `vol_global`, `created_at`
- Méthode : `get_real_volume_data(hours=48)`

### 3. Graphique Market Cap (`create_marketcap_chart`)
```python
def create_marketcap_chart(self, marketcap_data: pd.DataFrame = None) -> QWidget:
    if marketcap_data is None or marketcap_data.empty:
        marketcap_data = self.get_real_marketcap_data()
```

**Source de données :**
- Table : `BtcEthAlt_data`
- Colonnes : `mc_global`, `created_at`
- Méthode : `get_real_marketcap_data(hours=48)`

### 4. Graphique Candlestick (`create_candlestick_chart`)
```python
def create_candlestick_chart(self, ohlcv_data: pd.DataFrame = None, symbol="BTC/USDT") -> QWidget:
    if ohlcv_data is None or ohlcv_data.empty:
        ohlcv_data = self.get_real_ohlcv_data(symbol=symbol)
```

**Source de données :**
- Table : `ohlcv`
- Colonnes : `open`, `high`, `low`, `close`, `volume`, `timestamp`
- Méthode : `get_real_ohlcv_data(symbol, timeframe, limit)`

## 🔧 Nouvelles Méthodes de Récupération

### `get_real_dominance_data(hours=48)`
Récupère les données de dominance BTC/ETH/Alt des dernières heures.

### `get_real_volume_data(hours=48)`
Récupère les données de volume global des dernières heures.

### `get_real_marketcap_data(hours=48)`
Récupère les données de market cap global des dernières heures.

### `get_real_ohlcv_data(symbol, timeframe, limit)`
Récupère les données OHLCV pour un symbole donné depuis plusieurs exchanges.

## 📊 Structure de la Base de Données

### Table `BtcEthAlt_data`
```sql
CREATE TABLE BtcEthAlt_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    btc_mc REAL NOT NULL,           -- Market cap Bitcoin
    eth_mc REAL NOT NULL,           -- Market cap Ethereum  
    alt_mc REAL NOT NULL,           -- Market cap Altcoins
    mc_global REAL NOT NULL,        -- Market cap global
    btc_vol REAL NOT NULL,          -- Volume Bitcoin
    eth_vol REAL NOT NULL,          -- Volume Ethereum
    alt_vol REAL NOT NULL,          -- Volume Altcoins
    vol_global REAL NOT NULL,       -- Volume global
    btc_dom REAL NOT NULL,          -- Dominance Bitcoin (%)
    eth_dom REAL NOT NULL,          -- Dominance Ethereum (%)
    alt_dom REAL NOT NULL,          -- Dominance Altcoins (%)
    created_at TIMESTAMP DEFAULT (datetime('now', 'localtime'))
);
```

### Table `ohlcv`
```sql
CREATE TABLE ohlcv (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol_id TEXT NOT NULL,        -- Ex: 'BTC/USDT'
    exchange_id TEXT NOT NULL,      -- Ex: 'binance'
    timeframe TEXT NOT NULL,        -- Ex: '1h'
    timestamp TIMESTAMP NOT NULL,
    open REAL NOT NULL,
    high REAL NOT NULL,
    low REAL NOT NULL,
    close REAL NOT NULL,
    volume REAL NOT NULL,
    created_at TIMESTAMP DEFAULT (datetime('now', 'localtime'))
);
```

## 🚀 Utilisation

### Exemple basique :
```python
from nano_matplotlib_charts import MatplotlibChartGenerator

# Initialiser le générateur
chart_gen = MatplotlibChartGenerator(dark_mode=True)

# Créer des graphiques avec vraies données (automatique)
dominance_widget = chart_gen.create_dominance_chart()
volume_widget = chart_gen.create_volume_chart()
marketcap_widget = chart_gen.create_marketcap_chart()
candlestick_widget = chart_gen.create_candlestick_chart(symbol="BTC/USDT")
```

### Exemple avec données personnalisées :
```python
# Récupérer des données spécifiques
custom_data = chart_gen.get_real_dominance_data(hours=24)

# Utiliser ces données
dominance_widget = chart_gen.create_dominance_chart(custom_data)
```

## 🔄 Fallback sur Données Simulées

Si aucune vraie donnée n'est disponible, les graphiques utilisent automatiquement des données simulées réalistes :

```python
# Données par défaut si base vide
if dominance_data is None or dominance_data.empty:
    dates = pd.date_range(start='2024-01-01', periods=100, freq='h')
    btc_dom = 57 + np.random.randn(100) * 2  # ~57% ± 2%
    eth_dom = 18 + np.random.randn(100) * 1  # ~18% ± 1%
    alt_dom = 100 - btc_dom - eth_dom
```

## 🧪 Test des Connexions

Utilisez le script de test pour vérifier les connexions :

```bash
python test_real_data_charts.py
```

Ce script :
1. ✅ Vérifie la disponibilité des données
2. 📊 Affiche des statistiques sur les données
3. 🖼️ Lance une fenêtre avec 4 graphiques utilisant les vraies données

## 📝 Logs et Debugging

Les méthodes de récupération incluent des logs d'erreur :

```python
try:
    df = self.db.get_market_data(limit=hours * 20)
    # ...
except Exception as e:
    print(f"Error getting real dominance data: {e}")
    return pd.DataFrame()
```

## 🔧 Configuration

### Initialisation automatique :
```python
def __init__(self, dark_mode: bool = True):
    # ...
    if DATABASE_IMPORTS_OK:
        try:
            self.db = NanoDatabase()
            self.analyses = NanoAnalyses()
        except Exception as e:
            print(f"Warning: Could not initialize database connections: {e}")
```

### Vérification de disponibilité :
```python
if not self.db:
    return pd.DataFrame()  # Retourne DataFrame vide si pas de DB
```

## 🎯 Avantages

1. **🔄 Automatique** : Les graphiques récupèrent automatiquement les vraies données
2. **🛡️ Robuste** : Fallback sur données simulées si problème
3. **⚡ Performant** : Cache et optimisations dans les requêtes
4. **🎨 Flexible** : Possibilité de fournir des données personnalisées
5. **📊 Temps réel** : Données mises à jour automatiquement

## 🔮 Prochaines Étapes

- [ ] Ajouter cache pour éviter requêtes répétées
- [ ] Implémenter mise à jour automatique des graphiques
- [ ] Ajouter plus de types de graphiques connectés
- [ ] Optimiser les requêtes pour de gros volumes de données
