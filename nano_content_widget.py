#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ContentWidget avec NanoContainer intégré
Hérite de ContentWidget original et remplace le contenu par un NanoContainer
"""

from nano_dashboard import ContentWidget
from nano_container import <PERSON><PERSON><PERSON><PERSON>bChartContainer, NanoContainer
from nano_analyses import MarketAnalyzer
from PySide6.QtWidgets import QVBoxLayout
from PySide6.QtCore import QTimer

class ContentWidgetWithNanoContainer(ContentWidget):
    """ContentWidget original avec NanoContainer comme contenu"""
    
    def __init__(self, dark_mode=True, parent=None, container_config=None):
        # Configuration du container AVANT l'appel au parent
        self.container_config = container_config or {
            "title": "Container",
            "type": "normal",
            "analysis": "generic",
            "status": "neutral"
        }

        # Pré-initialiser le nano_container pour éviter les erreurs
        self.nano_container = None

        # Appeler le constructeur parent
        super().__init__(dark_mode, parent)

        # Créer et intégrer le NanoContainer APRÈS l'initialisation du parent
        self.setup_nano_container()
        
    def setup_nano_container(self):
        """Remplace le contenu du body par un NanoContainer"""

        # Vérifier que le body existe
        if not hasattr(self, 'body') or self.body is None:
            print("⚠️ Body non initialisé, report de la création du nano_container")
            return

        # Vider le body existant
        layout = self.body.layout()
        if layout:
            while layout.count():
                child = layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()

        try:
            # Créer le bon type de container
            if self.container_config["type"] == "plotly":
                # Déterminer si on doit afficher un graphique par défaut
                analysis_type = self.container_config.get("analysis", "generic")
                show_default = analysis_type not in ["forecast", "sentiment", "momentum"]
                self.nano_container = MatplotlibChartContainer(self, self.dark_mode, show_default)
            else:
                self.nano_container = NanoContainer(self, self.dark_mode)

            # Configuration initiale
            self.nano_container.set_banner_status(
                self.container_config["status"],
                self.container_config["title"]
            )
            self.nano_container.set_phrase("Initialisation...")

            # Ajouter au body
            if layout:
                layout.addWidget(self.nano_container)

            # Stocker la référence pour accès externe
            self.container_config["nano_container"] = self.nano_container

            print(f"✅ NanoContainer créé: {self.container_config['title']}")

        except Exception as e:
            print(f"⚠️ Erreur création NanoContainer: {e}")
            self.nano_container = None
        
    def update_dark_mode(self, dark_mode: bool):
        """Met à jour le mode sombre/clair"""
        # Appeler la méthode parent
        super().update_dark_mode(dark_mode)

        # Mettre à jour le nano container seulement s'il existe et n'est pas None
        if hasattr(self, 'nano_container') and self.nano_container is not None:
            self.nano_container.update_dark_mode(dark_mode)
    
    def get_nano_container(self):
        """Retourne le NanoContainer pour accès externe"""
        if hasattr(self, 'nano_container') and self.nano_container is not None:
            return self.nano_container
        return None
    
    def update_container_data(self, analyzer: MarketAnalyzer):
        """Met à jour les données du container selon son type d'analyse"""
        if not hasattr(self, 'nano_container'):
            return
            
        analysis_type = self.container_config["analysis"]
        
        try:
            if analysis_type == "dominance":
                self._update_dominance(analyzer)
            elif analysis_type == "volume":
                self._update_volume(analyzer)
            elif analysis_type == "marketcap":
                self._update_marketcap(analyzer)
            elif analysis_type == "altseason":
                self._update_altseason(analyzer)
            elif analysis_type == "risk":
                self._update_risk(analyzer)
            elif analysis_type == "capital_flows":
                self._update_capital_flows(analyzer)
            elif analysis_type == "orderbook":
                self._update_orderbook(analyzer)
            elif analysis_type == "vol_dominance":
                self._update_vol_dominance(analyzer)
            elif analysis_type == "correlation":
                self._update_correlation(analyzer)
            elif analysis_type == "forecast":
                self._update_forecast(analyzer)
            elif analysis_type == "sentiment":
                self._update_sentiment(analyzer)
            elif analysis_type == "momentum":
                self._update_momentum(analyzer)
            else:
                self._update_generic(analyzer)
                
        except Exception as e:
            self.nano_container.set_phrase(f"Erreur: {str(e)}")
    
    def _update_dominance(self, analyzer):
        """Met à jour le container de dominance"""
        try:
            dom_data = analyzer.get_dominance_data()
            dom_evolution = analyzer.get_dominance_evolution(48)
            status, phrase = analyzer.analyze_dominance_trend()

            self.nano_container.set_banner_status(status, f"🟡 BTC Dom: {dom_data['btc']:.1f}%")
            self.nano_container.set_phrase(phrase)

            # Utiliser la méthode spécifique pour dominance
            if hasattr(self.nano_container, 'create_dominance_chart'):
                self.nano_container.create_dominance_chart(dom_evolution)
            elif hasattr(self.nano_container, 'plot_dominance_plotly'):
                self.nano_container.plot_dominance_plotly(dom_evolution)
        except Exception as e:
            print(f"Erreur dominance update: {e}")
            self.nano_container.set_banner_status("neutral", "🟡 BTC Dominance")
            self.nano_container.set_phrase(f"Erreur: {str(e)}")
            # Essayer d'afficher un graphique vide
            if hasattr(self.nano_container, 'create_dominance_chart'):
                import pandas as pd
                self.nano_container.create_dominance_chart(pd.DataFrame())
    
    def _update_price(self, analyzer, symbol):
        """Met à jour un container de prix"""
        status, phrase = analyzer.analyze_price_trend(symbol)
        variations = analyzer.calculate_variations(symbol)

        symbol_short = symbol.split('/')[0]
        self.nano_container.set_banner_status(status, f"{symbol_short} Analysis")
        self.nano_container.set_phrase(phrase)
        self.nano_container.update_variations(variations)

        # Utiliser la méthode spécifique pour les prix
        if hasattr(self.nano_container, 'create_candlestick_chart'):
            try:
                ohlcv_data = analyzer.get_ohlcv_data(symbol, "1h", 100)
                self.nano_container.create_candlestick_chart(ohlcv_data, f"{symbol} Price Chart")
            except Exception as e:
                print(f"Erreur candlestick chart: {e}")
                import pandas as pd
                self.nano_container.create_candlestick_chart(pd.DataFrame(), f"{symbol} Price Chart")
        elif hasattr(self.nano_container, 'create_price_chart'):
            try:
                price_data = analyzer.get_price_evolution(symbol, 48)
                self.nano_container.create_price_chart(price_data, symbol_short, f"{symbol_short} Price Evolution")
            except Exception as e:
                print(f"Erreur price chart: {e}")
                import pandas as pd
                self.nano_container.create_price_chart(pd.DataFrame(), symbol_short, f"{symbol_short} Price Evolution")
        elif hasattr(self.nano_container, 'plot_candlestick_plotly'):
            try:
                ohlcv_data = analyzer.get_ohlcv_data(symbol, "1h", 100)
                self.nano_container.plot_candlestick_plotly(ohlcv_data, f"{symbol} Chart")
            except Exception as e:
                print(f"Erreur plotly chart: {e}")
    
    def _update_volume(self, analyzer):
        """Met à jour le container de volume"""
        vol_data = analyzer.get_volume_data()
        status, phrase = analyzer.analyze_volume_trend()

        self.nano_container.set_banner_status(status, f"📊 Vol: ${vol_data['global']/1e9:.1f}B")
        self.nano_container.set_phrase(phrase)

        # Utiliser la méthode spécifique pour volume
        if hasattr(self.nano_container, 'create_volume_chart'):
            try:
                volume_evolution = analyzer.get_volume_evolution(48)
                self.nano_container.create_volume_chart(volume_evolution)
            except Exception as e:
                print(f"Erreur volume chart: {e}")
                import pandas as pd
                self.nano_container.create_volume_chart(pd.DataFrame())
        elif hasattr(self.nano_container, 'plot_volume_plotly'):
            import pandas as pd
            volume_evolution = pd.DataFrame()
            self.nano_container.plot_volume_plotly(volume_evolution)
    
    def _update_marketcap(self, analyzer):
        """Met à jour le container de market cap"""
        try:
            mc_data = analyzer.get_marketcap_data()
            status, phrase = analyzer.analyze_capital_flows()

            self.nano_container.set_banner_status(status, f"💰 MC: ${mc_data['global']/1e12:.2f}T")
            self.nano_container.set_phrase(phrase)

            # Utiliser la méthode spécifique pour market cap
            if hasattr(self.nano_container, 'create_marketcap_chart'):
                mc_evolution = analyzer.get_marketcap_evolution(48)
                self.nano_container.create_marketcap_chart(mc_evolution)
            elif hasattr(self.nano_container, 'plot_marketcap_plotly'):
                mc_evolution = analyzer.get_marketcap_evolution(48)
                self.nano_container.plot_marketcap_plotly(mc_evolution)
        except Exception as e:
            print(f"Erreur marketcap update: {e}")
            self.nano_container.set_banner_status("neutral", "💰 Market Cap")
            self.nano_container.set_phrase(f"Erreur: {str(e)}")
            # Essayer d'afficher un graphique vide
            if hasattr(self.nano_container, 'create_marketcap_chart'):
                import pandas as pd
                self.nano_container.create_marketcap_chart(pd.DataFrame())
    
    def _update_altseason(self, analyzer):
        """Met à jour le container altseason avec gauge"""
        alt_index, alt_msg = analyzer.calculate_altseason_index()

        if alt_index:
            if alt_index > 120:
                status = "bullish"
            elif alt_index < 80:
                status = "bearish"
            else:
                status = "neutral"
            self.nano_container.set_banner_status(status, f"🌀 Altseason: {alt_index:.0f}")
        else:
            self.nano_container.set_banner_status("neutral", "🌀 Altseason Index")

        self.nano_container.set_phrase(alt_msg)

        # Utiliser le gauge d'altseason
        if hasattr(self.nano_container, 'create_altseason_gauge'):
            self.nano_container.create_altseason_gauge(alt_index, alt_msg)
    
    def _update_risk(self, analyzer):
        """Met à jour le container de risque avec gauge"""
        risk_index, risk_msg = analyzer.calculate_risk_index()

        if risk_index:
            if risk_index < 30:
                status = "bearish"
            elif risk_index > 70:
                status = "bullish"
            else:
                status = "neutral"
            self.nano_container.set_banner_status(status, f"⚠️ Risk: {risk_index:.0f}")
        else:
            self.nano_container.set_banner_status("neutral", "⚠️ Risk Index")

        self.nano_container.set_phrase(risk_msg)

        # Utiliser le gauge de risque
        if hasattr(self.nano_container, 'create_risk_gauge'):
            self.nano_container.create_risk_gauge(risk_index, risk_msg)
    
    def _update_correlation(self, analyzer):
        """Met à jour le container de corrélation"""
        self.nano_container.set_banner_status("neutral", "🔄 Correlation Matrix")
        self.nano_container.set_phrase("Matrice de corrélation entre cryptomonnaies")

        # Vérifier que c'est un MatplotlibChartContainer avec chart_generator
        if (hasattr(self.nano_container, 'chart_generator') and
            self.nano_container.chart_generator is not None):
            try:
                import pandas as pd
                corr_data = pd.DataFrame()
                self.nano_container.create_correlation_heatmap(corr_data)
                self.nano_container.set_phrase("Matrice de corrélation affichée dans le dashboard")
            except Exception as e:
                self.nano_container.set_phrase(f"Erreur génération heatmap: {str(e)}")
        else:
            # Container normal ou matplotlib non disponible
            self.nano_container.set_phrase("Matrice de corrélation (matplotlib requis pour graphique intégré)")
    
    def _update_generic(self, analyzer):
        """Met à jour un container générique"""
        self.nano_container.set_banner_status(
            self.container_config["status"], 
            self.container_config["title"]
        )
        self.nano_container.set_phrase(f"Analyse {self.container_config['analysis']} en développement...")
        
        # Variations aléatoires pour test
        import random
        test_variations = {
            "1m": random.uniform(-2, 2),
            "5m": random.uniform(-3, 3),
            "15m": random.uniform(-5, 5),
            "1h": random.uniform(-8, 8),
            "4h": random.uniform(-12, 12),
            "1d": random.uniform(-20, 20)
        }
        self.nano_container.update_variations(test_variations)

    def _update_capital_flows(self, analyzer):
        """Met à jour le container de flux de capitaux"""
        self.nano_container.set_banner_status("neutral", "💸 Capital Flows")
        self.nano_container.set_phrase("Analyse des flux de capitaux entre BTC, ETH et Altcoins")

        # Créer des données de flux simulées (à implémenter dans analyzer)
        import pandas as pd
        flows_data = pd.DataFrame()  # Données vides pour l'instant

        if hasattr(self.nano_container, 'create_capital_flows_chart'):
            self.nano_container.create_capital_flows_chart(flows_data)

    def _update_orderbook(self, analyzer):
        """Met à jour le container de profondeur du carnet d'ordres"""
        self.nano_container.set_banner_status("neutral", "📚 Orderbook Depth")
        self.nano_container.set_phrase("Profondeur du carnet d'ordres BTC/USDT")

        # Données d'orderbook simulées (à implémenter dans analyzer)
        orderbook_data = None  # Données vides pour l'instant

        if hasattr(self.nano_container, 'create_orderbook_chart'):
            self.nano_container.create_orderbook_chart(orderbook_data, "BTC/USDT")

    def _update_vol_dominance(self, analyzer):
        """Met à jour le container volume vs dominance"""
        self.nano_container.set_banner_status("bullish", "🔄 Vol vs Dominance")
        self.nano_container.set_phrase("Corrélation entre volume global et dominance BTC")

        # Données de corrélation simulées (à implémenter dans analyzer)
        import pandas as pd
        vol_dom_data = pd.DataFrame()  # Données vides pour l'instant

        if hasattr(self.nano_container, 'create_vol_dominance_scatter'):
            self.nano_container.create_vol_dominance_scatter(vol_dom_data)

    def _update_forecast(self, analyzer):
        """Met à jour le container de prévision de prix"""
        self.nano_container.set_banner_status("neutral", "📈 Price Forecast")

        # Utiliser les méthodes de forecast du reference.py
        try:
            # Simuler un forecast BTC (à implémenter dans analyzer)
            forecast_msg = "BTC consolide autour de 45000$, attente d'un breakout."
            self.nano_container.set_phrase(forecast_msg)
        except:
            self.nano_container.set_phrase("Prévision de prix en développement...")

    def _update_sentiment(self, analyzer):
        """Met à jour le container de sentiment de marché"""
        self.nano_container.set_banner_status("neutral", "🎯 Market Sentiment")
        self.nano_container.set_phrase("Analyse du sentiment global du marché crypto")

    def _update_momentum(self, analyzer):
        """Met à jour le container de momentum de tendance"""
        self.nano_container.set_banner_status("bullish", "🌊 Trend Momentum")
        self.nano_container.set_phrase("Analyse du momentum des tendances de marché")

# Configurations prédéfinies pour les 12 containers - NOUVELLES IDÉES du reference.py
CONTAINER_CONFIGS = [
    # Row 0
    {"title": "🟡 BTC Dominance", "type": "plotly", "analysis": "dominance", "status": "bullish"},
    {"title": "🌀 Altseason Index", "type": "plotly", "analysis": "altseason", "status": "neutral"},
    {"title": "⚠️ Global Risk Index", "type": "plotly", "analysis": "risk", "status": "bearish"},
    {"title": "📊 Global Volume", "type": "plotly", "analysis": "volume", "status": "bullish"},

    # Row 1
    {"title": "💰 Market Cap", "type": "plotly", "analysis": "marketcap", "status": "bullish"},
    {"title": "💸 Capital Flows", "type": "plotly", "analysis": "capital_flows", "status": "neutral"},
    {"title": "📚 Orderbook Depth", "type": "plotly", "analysis": "orderbook", "status": "neutral"},
    {"title": "🔄 Vol vs Dominance", "type": "plotly", "analysis": "vol_dominance", "status": "bullish"},

    # Row 2
    {"title": "🔄 Correlation Matrix", "type": "plotly", "analysis": "correlation", "status": "neutral"},
    {"title": "📈 Price Forecast", "type": "plotly", "analysis": "forecast", "status": "neutral"},
    {"title": "🎯 Market Sentiment", "type": "plotly", "analysis": "sentiment", "status": "neutral"},
    {"title": "🌊 Trend Momentum", "type": "plotly", "analysis": "momentum", "status": "bullish"}
]
