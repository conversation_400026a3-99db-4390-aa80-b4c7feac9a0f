#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Module des containers d'analyse pour NanoMarketSensor
Structure normalisée : 2 rows (75% graphique + 25% banner/phrase)
Row 0 : 2 colonnes (75% graphique + 25% tableau variations)
"""

import random
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QTableWidget,
    QTableWidgetItem, QHeaderView, QFrame, QSizePolicy
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont
from nano_style import (
    get_container_style, get_chart_area_style, get_variation_table_style,
    get_banner_style, get_phrase_style, get_chart_fallback_style, get_chart_info_style
)

import random
import math
try:
    from nano_matplotlib_charts import MatplotlibChartGenerator
    MATPLOTLIB_AVAILABLE = True
    print("✅ Matplotlib disponible - graphiques intégrés activés")
except ImportError as e:
    MATPLOTLIB_AVAILABLE = False
    print(f"⚠️ Matplotlib non disponible - graphiques simulés: {e}")
except Exception as e:
    MATPLOTLIB_AVAILABLE = False
    print(f"⚠️ Erreur Matplotlib - graphiques simulés: {e}")

class NanoContainer(QWidget):
    """Container d'analyse normalisé avec structure 75%/25%"""

    def __init__(self, parent=None, dark_mode=True):
        super().__init__(parent)
        self.dark_mode = dark_mode

        self._min_width = 300
        self._min_height = 200
        self.setup_ui()
        self.setup_responsive()
        
    def setup_ui(self):
        """Configure l'interface du container"""
        # Layout principal vertical
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # Row 0: Zone graphique + tableau (75% de la hauteur)
        chart_row = QWidget()
        # Politique de taille pour s'adapter au parent
        chart_row.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        chart_layout = QHBoxLayout(chart_row)
        chart_layout.setContentsMargins(0, 0, 0, 0)
        
        chart_layout.setSpacing(5)

        # Colonne 0: Zone graphique (75% de la largeur)
        self.chart_area = QWidget()
        self.chart_area.setStyleSheet(get_chart_area_style(self.dark_mode))
        self.chart_area.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.chart_area.setMaximumHeight(240)  # Hauteur maximale
        chart_layout.addWidget(self.chart_area, 3)  # 75%

        # Colonne 1: Tableau des variations (25% de la largeur)
        self.variation_table = self.create_variation_table()
        chart_layout.addWidget(self.variation_table, 1)  # 25%
        self.variation_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.variation_table.setMaximumHeight(240)  # Hauteur maximale

        main_layout.addWidget(chart_row, 3)  # 75% de la hauteur

        # Row 1: Banner + phrase (25% de la hauteur)
        info_row = QWidget()
        # Hauteur maximale adaptative selon la taille du parent
        
        info_row.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Maximum)
        info_row.setMaximumHeight(60)  # Hauteur encore plus réduite
        info_layout = QVBoxLayout(info_row)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(2)

        # Banner
        self.banner_label = QLabel("Container Title")
    
        self.banner_label.setAlignment(Qt.AlignCenter)
        self.banner_label.setMinimumHeight(35)  # Limiter la hauteur du banner
        self.set_banner_status("neutral")
        info_layout.addWidget(self.banner_label)

        # Phrase d'analyse
        self.phrase_label = QLabel("Analyse en cours...")
        self.phrase_label.setStyleSheet(get_phrase_style(self.dark_mode))
     
        self.phrase_label.setAlignment(Qt.AlignCenter)
        self.phrase_label.setWordWrap(True)
        self.phrase_label.setMinimumHeight(35)  # Limiter la hauteur de la phrase
        info_layout.addWidget(self.phrase_label)

        main_layout.addWidget(info_row, 1)  # 25% de la hauteur

        # Style général du container
        self.update_dark_mode(self.dark_mode)

    def setup_responsive(self):
        """Configure le comportement responsive"""
        self.setMinimumSize(self._min_width, self._min_height)

        # Politique de taille - s'adapter au parent SANS dépasser
        self.setSizePolicy(
            QSizePolicy.Expanding,
            QSizePolicy.Expanding
        )

        # Permettre au widget de s'adapter à la taille du parent
        # mais ne pas forcer une taille maximale qui pourrait causer des débordements
        self.setMaximumSize(16777215, 16777215)  # Taille maximale Qt

    def update_dark_mode(self, dark_mode: bool):
        """Met à jour le mode sombre/clair"""
        self.dark_mode = dark_mode

        # Mettre à jour tous les styles
        self.setStyleSheet(get_container_style(self.dark_mode))
        self.chart_area.setStyleSheet(get_chart_area_style(self.dark_mode))
        self.variation_table.setStyleSheet(get_variation_table_style(self.dark_mode))
        self.phrase_label.setStyleSheet(get_phrase_style(self.dark_mode))

        # Forcer la mise à jour
        self.update()

    def resizeEvent(self, event):
        """Gère le redimensionnement responsive"""
        super().resizeEvent(event)

        # Ajuster la taille des éléments selon la taille du container
        width = event.size().width()
        height = event.size().height()

        # Ajuster la hauteur du tableau selon la taille
        if hasattr(self, 'variation_table'):
            if width < 400:  # Petit écran
                self.variation_table.setMaximumWidth(80)
            elif width < 600:  # Écran moyen
                self.variation_table.setMaximumWidth(100)
            else:  # Grand écran
                self.variation_table.setMaximumWidth(120)

        # Ajuster la hauteur de la zone info selon la hauteur totale
        if hasattr(self, 'banner_label') and hasattr(self, 'phrase_label'):
            # Calculer une hauteur proportionnelle pour la zone info (max 20% de la hauteur totale)
            max_info_height = max(50, min(80, int(height * 0.2)))

            # Ajuster les hauteurs des éléments
            banner_height = max(20, min(30, int(max_info_height * 0.4)))
            phrase_height = max(25, min(40, int(max_info_height * 0.6)))

            self.banner_label.setMaximumHeight(banner_height)
            self.phrase_label.setMaximumHeight(phrase_height)

        # Ajuster la police selon la taille
        if hasattr(self, 'banner_label'):
            if width < 400:
                font_size = "11px"
            elif width < 600:
                font_size = "12px"
            else:
                font_size = "14px"

            # Mettre à jour le style du banner avec la nouvelle taille
            current_style = self.banner_label.styleSheet()
            if "font-size:" in current_style:
                # Remplacer la taille existante
                import re
                new_style = re.sub(r'font-size:\s*\d+px', f'font-size: {font_size}', current_style)
                self.banner_label.setStyleSheet(new_style)
        
    def create_variation_table(self):
        """Crée le tableau des variations"""
        table = QTableWidget(0, 2)
        table.setHorizontalHeaderLabels(["TF", "Δ%"])
        table.setStyleSheet(get_variation_table_style(self.dark_mode))
        
        # Configuration du tableau
        table.horizontalHeader().setStretchLastSection(True)
        table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        table.verticalHeader().setVisible(False)
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # Données par défaut
        timeframes = ["1m", "5m", "15m", "1h", "4h", "1d", "1w"]
        for i, tf in enumerate(timeframes):
            table.insertRow(i)
            table.setItem(i, 0, QTableWidgetItem(tf))
            table.setItem(i, 1, QTableWidgetItem("--"))
            
        return table
        
    def set_banner_status(self, status: str, text: str = None):
        """Met à jour le banner avec le statut"""
        if text:
            self.banner_label.setText(text)
        self.banner_label.setStyleSheet(get_banner_style(status))
        
    def set_phrase(self, text: str):
        """Met à jour la phrase d'analyse"""
        self.phrase_label.setText(text)
        
    def update_variations(self, variations_data: dict):
        """Met à jour le tableau des variations"""
        for i in range(self.variation_table.rowCount()):
            tf_item = self.variation_table.item(i, 0)
            if tf_item and tf_item.text() in variations_data:
                value = variations_data[tf_item.text()]
                delta_item = QTableWidgetItem(f"{value:+.2f}%")
                
                # Coloration selon la valeur
                if value > 0:
                    delta_item.setForeground(Qt.green)
                elif value < 0:
                    delta_item.setForeground(Qt.red)
                else:
                    delta_item.setForeground(Qt.gray)
                    
                self.variation_table.setItem(i, 1, delta_item)

class ChartContainer(NanoContainer):
    """Container avec zone graphique (Plotly ou simulée)"""

    def __init__(self, parent=None, dark_mode=True):
        super().__init__(parent, dark_mode)
        self.setup_chart()

    def update_dark_mode(self, dark_mode: bool):
        """Met à jour le mode sombre/clair pour le container avec graphique"""
        super().update_dark_mode(dark_mode)

        # Mettre à jour le style du graphique
        if hasattr(self, 'chart_label'):
            bg_color = '#0d1117' if dark_mode else '#f8f9fa'
            text_color = '#c9d1d9' if dark_mode else '#333333'
            border_color = '#30363d' if dark_mode else '#dee2e6'

            self.chart_label.setStyleSheet(f"""
                QLabel {{
                    background-color: {bg_color};
                    color: {text_color};
                    border: 1px solid {border_color};
                    border-radius: 8px;
                    padding: 20px;
                    font-size: 14px;
                    min-height: 200px;
                }}
            """)

    def setup_chart(self):
        """Configure la zone graphique simulée"""
        # Layout pour la zone graphique
        chart_layout = QVBoxLayout(self.chart_area)
        chart_layout.setContentsMargins(5, 5, 5, 5)

        # Label pour simuler un graphique
        self.chart_label = QLabel("📊 Graphique en cours de chargement...")
        self.chart_label.setAlignment(Qt.AlignCenter)
        self.chart_label.setStyleSheet(get_chart_fallback_style(self.dark_mode))

        chart_layout.addWidget(self.chart_label)

        # Simuler un graphique par défaut
        self.plot_default()

    def plot_default(self):
        """Graphique par défaut simulé"""
        self.chart_label.setText("📈 Graphique par défaut\n\n~ Simulation de données ~\n\nLigne sinusoïdale")

    def clear_plot(self):
        """Efface le graphique"""
        self.chart_label.setText("📊 Graphique effacé")

    def plot_line(self, x_data, y_data, title="", xlabel="", ylabel="", color=None):
        """Simule un graphique linéaire"""
        if not x_data or not y_data:
            self.chart_label.setText(f"📈 {title}\n\n❌ Pas de données")
            return

        min_val = min(y_data)
        max_val = max(y_data)
        avg_val = sum(y_data) / len(y_data)

        trend = "📈 Haussier" if y_data[-1] > y_data[0] else "📉 Baissier"

        self.chart_label.setText(f"""📈 {title}

{xlabel} → {ylabel}

Min: {min_val:.2f}
Max: {max_val:.2f}
Moy: {avg_val:.2f}

{trend}

Points: {len(x_data)}""")

    def plot_candlestick(self, ohlc_data, title=""):
        """Simule un graphique en chandelles"""
        if not ohlc_data:
            # Simulation de données OHLC
            price = 50000 + random.randint(-5000, 5000)
            trend = "📈 Haussier" if random.choice([True, False]) else "📉 Baissier"
            volatility = random.uniform(1, 5)

            self.chart_label.setText(f"""🕯️ {title}

~ Simulation OHLCV ~

Prix actuel: ${price:,.0f}
Tendance: {trend}
Volatilité: {volatility:.1f}%

Bougies: 50
Timeframe: 1h""")
        else:
            self.chart_label.setText(f"🕯️ {title}\n\nDonnées OHLC: {len(ohlc_data)} bougies")

class MatplotlibChartContainer(NanoContainer):
    """Container avec graphiques matplotlib intégrés dans le dashboard"""

    def __init__(self, parent=None, dark_mode=True, show_default_chart=True):
        # Initialiser les attributs avant l'appel super()
        self.chart_generator = None
        self.current_chart_widget = None
        self.show_default_chart = show_default_chart

        super().__init__(parent, dark_mode)
        self.setup_matplotlib_chart()

    def setup_matplotlib_chart(self):
        """Configure le générateur de graphiques matplotlib"""
        if not MATPLOTLIB_AVAILABLE:
            # Fallback vers simulation
            self.setup_chart_fallback()
            return

        try:
            # Créer le générateur de graphiques
            self.chart_generator = MatplotlibChartGenerator(self.dark_mode)

            # Layout pour la zone graphique
            chart_layout = QVBoxLayout(self.chart_area)
            chart_layout.setContentsMargins(5, 5, 5, 5)

            # Créer un graphique par défaut seulement si demandé
            if self.show_default_chart:
                self.display_default_chart()
            else:
                # Afficher un message d'attente au lieu du graphique par défaut
                self.display_waiting_message()

        except Exception as e:
            print(f"Erreur setup Matplotlib: {e}")
            self.setup_chart_fallback()

    def display_default_chart(self):
        """Affiche un graphique par défaut"""
        if self.chart_generator:
            # Créer un graphique linéaire par défaut
            x_data = list(range(50))
            y_data = [50000 + i*100 + random.randint(-500, 500) for i in x_data]
            chart_widget = self.chart_generator.create_line_chart(
                x_data, y_data, "Default Chart", "Time", "Value"
            )
            self.display_chart_widget(chart_widget)

    def display_waiting_message(self):
        """Affiche un message d'attente au lieu d'un graphique par défaut"""
        # Créer un label d'attente
        waiting_label = QLabel("📊 En attente de données...\n\nLe graphique s'affichera\nlorsque les données seront disponibles")
        waiting_label.setAlignment(Qt.AlignCenter)
        waiting_label.setStyleSheet(get_chart_fallback_style(self.dark_mode))

        # Ajouter au layout de la zone graphique
        chart_layout = self.chart_area.layout()
        if not chart_layout:
            chart_layout = QVBoxLayout(self.chart_area)
            chart_layout.setContentsMargins(5, 5, 5, 5)

        chart_layout.addWidget(waiting_label)
        self.current_chart_widget = waiting_label

    def display_chart_widget(self, chart_widget):
        """Affiche un widget graphique dans la zone chart"""
        # Supprimer l'ancien widget s'il existe
        if self.current_chart_widget:
            self.current_chart_widget.setParent(None)
            self.current_chart_widget.deleteLater()

        # Ajouter le nouveau widget
        chart_layout = self.chart_area.layout()
        if not chart_layout:
            chart_layout = QVBoxLayout(self.chart_area)
            chart_layout.setContentsMargins(5, 5, 5, 5)

        chart_layout.addWidget(chart_widget)
        self.current_chart_widget = chart_widget

    def create_line_chart(self, x_data, y_data, title="Line Chart", xlabel="X", ylabel="Y"):
        """Crée et affiche un graphique linéaire"""
        if self.chart_generator:
            chart_widget = self.chart_generator.create_line_chart(x_data, y_data, title, xlabel, ylabel)
            self.display_chart_widget(chart_widget)

    def create_candlestick_chart(self, ohlcv_data, title="Candlestick Chart"):
        """Crée et affiche un graphique en chandelles"""
        if self.chart_generator:
            chart_widget = self.chart_generator.create_candlestick_chart(ohlcv_data, title)
            self.display_chart_widget(chart_widget)

    def create_dominance_chart(self, dominance_data):
        """Crée et affiche un graphique de dominance"""
        if self.chart_generator:
            chart_widget = self.chart_generator.create_dominance_chart(dominance_data)
            self.display_chart_widget(chart_widget)

    def create_volume_chart(self, volume_data):
        """Crée et affiche un graphique de volume"""
        if self.chart_generator:
            chart_widget = self.chart_generator.create_volume_chart(volume_data)
            self.display_chart_widget(chart_widget)

    def create_correlation_heatmap(self, corr_data):
        """Crée et affiche une heatmap de corrélation"""
        if self.chart_generator:
            chart_widget = self.chart_generator.create_correlation_heatmap(corr_data)
            self.display_chart_widget(chart_widget)

    def create_marketcap_chart(self, marketcap_data):
        """Crée et affiche un graphique de market cap"""
        if self.chart_generator:
            chart_widget = self.chart_generator.create_marketcap_chart(marketcap_data)
            self.display_chart_widget(chart_widget)

    def create_altseason_gauge(self, altseason_index, altseason_msg):
        """Crée et affiche un gauge d'altseason"""
        if self.chart_generator:
            chart_widget = self.chart_generator.create_altseason_gauge(altseason_index, altseason_msg)
            self.display_chart_widget(chart_widget)

    def create_risk_gauge(self, risk_index, risk_msg):
        """Crée et affiche un gauge de risque"""
        if self.chart_generator:
            chart_widget = self.chart_generator.create_risk_gauge(risk_index, risk_msg)
            self.display_chart_widget(chart_widget)

    def create_capital_flows_chart(self, flows_data):
        """Crée et affiche un graphique de flux de capitaux"""
        if self.chart_generator:
            chart_widget = self.chart_generator.create_capital_flows_chart(flows_data)
            self.display_chart_widget(chart_widget)

    def create_orderbook_chart(self, orderbook_data, symbol="BTC/USDT"):
        """Crée et affiche un graphique de profondeur du carnet d'ordres"""
        if self.chart_generator:
            chart_widget = self.chart_generator.create_orderbook_chart(orderbook_data, symbol)
            self.display_chart_widget(chart_widget)

    def create_vol_dominance_scatter(self, vol_dom_data):
        """Crée et affiche un scatter plot volume vs dominance"""
        if self.chart_generator:
            chart_widget = self.chart_generator.create_vol_dominance_scatter(vol_dom_data)
            self.display_chart_widget(chart_widget)

    def create_price_chart(self, price_data, symbol="BTC", title=None):
        """Crée et affiche un graphique de prix spécifique"""
        if self.chart_generator:
            if not title:
                title = f"{symbol} Price Evolution"

            # Extraire les données de prix
            if hasattr(price_data, 'index') and hasattr(price_data, 'values'):
                x_data = list(range(len(price_data)))
                y_data = price_data.values.tolist() if hasattr(price_data.values, 'tolist') else list(price_data.values)
            else:
                x_data = list(range(50))
                y_data = [50000 + i*100 + random.randint(-500, 500) for i in x_data]

            chart_widget = self.chart_generator.create_line_chart(
                x_data, y_data, title, "Time", f"{symbol} Price ($)"
            )
            self.display_chart_widget(chart_widget)

    def update_dark_mode(self, dark_mode: bool):
        """Met à jour le mode sombre/clair"""
        super().update_dark_mode(dark_mode)
        if self.chart_generator:
            self.chart_generator.update_dark_mode(dark_mode)
            # Redessiner le graphique actuel
            if self.current_chart_widget:
                self.display_default_chart()

    def setup_chart_fallback(self):
        """Fallback si Matplotlib non disponible"""
        chart_layout = QVBoxLayout(self.chart_area)
        chart_layout.setContentsMargins(5, 5, 5, 5)

        self.chart_label = QLabel("📊 Matplotlib non disponible - Mode simulation")
        self.chart_label.setAlignment(Qt.AlignCenter)
        self.update_chart_label_style()
        chart_layout.addWidget(self.chart_label)

    def update_chart_label_style(self):
        """Met à jour le style du label de fallback"""
        if hasattr(self, 'chart_label'):
            self.chart_label.setStyleSheet(get_chart_fallback_style(self.dark_mode))

    def update_chart_info_style(self):
        """Met à jour le style du label d'information"""
        if hasattr(self, 'chart_info_label'):
            self.chart_info_label.setStyleSheet(get_chart_info_style(self.dark_mode))

    def update_dark_mode(self, dark_mode: bool):
        """Met à jour le mode sombre/clair pour Plotly"""
        super().update_dark_mode(dark_mode)

        if self.chart_generator:
            self.chart_generator.update_dark_mode(dark_mode)
            self.update_chart_info_style()
        elif hasattr(self, 'chart_label'):
            self.update_chart_label_style()



    def display_chart_info(self, title: str, html_file: str, chart_type: str):
        """Affiche les informations du graphique et permet l'ouverture"""
        if not hasattr(self, 'chart_info_label'):
            return

        import webbrowser
        import os

        # Informations du graphique
        file_name = os.path.basename(html_file) if html_file else "N/A"

        info_text = f"""📊 {title}

Type: {chart_type}
Statut: ✅ Généré avec Plotly
Fichier: {file_name}

💡 Double-cliquez pour ouvrir
dans votre navigateur"""

        self.chart_info_label.setText(info_text)

        # Permettre l'ouverture du fichier au double-clic
        if html_file and os.path.exists(html_file):
            self.chart_info_label.mousePressEvent = lambda event: self.open_chart_in_browser(html_file)

    def open_chart_in_browser(self, html_file: str):
        """Ouvre le graphique dans le navigateur"""
        try:
            import webbrowser
            webbrowser.open(f"file://{html_file}")
            print(f"🚀 Graphique ouvert: {html_file}")
        except Exception as e:
            print(f"Erreur ouverture navigateur: {e}")



    def plot_dominance_plotly(self, dominance_data):
        """Trace un graphique de dominance avec Plotly"""
        if not self.chart_generator:
            return

        try:
            import pandas as pd

            if not isinstance(dominance_data, pd.DataFrame):
                dominance_data = pd.DataFrame()

            html_file = self.chart_generator.create_dominance_chart(dominance_data)

            if html_file:
                self.display_chart_info("Market Dominance", html_file, "Multi-Line Chart")
                self.current_chart_file = html_file

        except Exception as e:
            print(f"Erreur plot dominance: {e}")


