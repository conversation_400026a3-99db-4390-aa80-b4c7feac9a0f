#!/usr/bin/env python3
"""
Exemple simple d'utilisation des graphiques connectés aux vraies données
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PySide6.QtCore import Qt

# Ajouter le répertoire courant au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nano_matplotlib_charts import MatplotlibChartGenerator

class ExempleGraphiques(QMainWindow):
    """Exemple simple d'utilisation des graphiques"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📊 Graphiques Connectés aux Vraies Données")
        self.setGeometry(100, 100, 1000, 700)
        
        # Initialiser le générateur de graphiques
        self.chart_generator = MatplotlibChartGenerator(dark_mode=True)
        
        self.init_ui()
    
    def init_ui(self):
        """Initialise l'interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Titre
        title = QLabel("📊 Graphiques avec Vraies Données de la Base SQLite")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #58a6ff;
                padding: 10px;
                background-color: #0d1117;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        # Explication
        explanation = QLabel("""
        🔗 Ces graphiques sont automatiquement connectés aux vraies données de votre base SQLite.
        
        ✅ Dominance BTC/ETH/Alt : Données de la table 'BtcEthAlt_data'
        ✅ Volume Global : Données réelles de volume crypto
        ✅ Market Cap : Capitalisation de marché en temps réel
        ✅ Candlestick : Données OHLCV de la table 'ohlcv'
        
        Si aucune donnée n'est disponible, des données simulées réalistes sont utilisées.
        """)
        explanation.setWordWrap(True)
        explanation.setStyleSheet("""
            QLabel {
                color: #c9d1d9;
                padding: 10px;
                background-color: #161b22;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(explanation)
        
        # Créer et ajouter le graphique de dominance
        try:
            dominance_chart = self.chart_generator.create_dominance_chart()
            layout.addWidget(dominance_chart)
            
            status = QLabel("✅ Graphique de dominance chargé avec succès")
            status.setStyleSheet("color: #56d364; padding: 5px;")
            layout.addWidget(status)
            
        except Exception as e:
            error_label = QLabel(f"❌ Erreur: {e}")
            error_label.setStyleSheet("color: #f85149; padding: 5px;")
            layout.addWidget(error_label)

def main():
    """Fonction principale"""
    print("🚀 Lancement de l'exemple de graphiques connectés")
    
    app = QApplication(sys.argv)
    
    # Style sombre
    app.setStyleSheet("""
        QMainWindow {
            background-color: #0d1117;
            color: #c9d1d9;
        }
        QWidget {
            background-color: #0d1117;
            color: #c9d1d9;
        }
    """)
    
    window = ExempleGraphiques()
    window.show()
    
    print("✅ Fenêtre affichée - Le graphique utilise les vraies données de votre base")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
