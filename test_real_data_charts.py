#!/usr/bin/env python3
"""
Test des graphiques matplotlib avec les vraies données de la base
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QHBoxLayout, QLabel
from PySide6.QtCore import Qt

# Ajouter le répertoire courant au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from nano_matplotlib_charts import MatplotlibChartGenerator
    from nano_database import MarketDatabase
    from nano_analyses import MarketAnalyzer
    print("✅ Imports réussis")
except ImportError as e:
    print(f"❌ Erreur d'import: {e}")
    sys.exit(1)

class TestChartsWindow(QMainWindow):
    """Fenêtre de test pour les graphiques avec vraies données"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Graphiques - Vraies Données")
        self.setGeometry(100, 100, 1400, 900)
        
        # Initialiser les composants
        self.init_data_sources()
        self.init_ui()
        self.load_charts()
    
    def init_data_sources(self):
        """Initialise les sources de données"""
        try:
            self.db = MarketDatabase()
            self.analyses = MarketAnalyzer()
            self.chart_generator = MatplotlibChartGenerator(dark_mode=True)
            print("✅ Sources de données initialisées")
        except Exception as e:
            print(f"❌ Erreur initialisation: {e}")
            self.db = None
            self.analyses = None
            self.chart_generator = None
    
    def init_ui(self):
        """Initialise l'interface utilisateur"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        
        # Titre
        title = QLabel("📊 Test des Graphiques avec Vraies Données")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #58a6ff;
                padding: 10px;
                background-color: #0d1117;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title)
        
        # Layout pour les graphiques (2x2)
        charts_layout = QVBoxLayout()
        
        # Première ligne
        row1_layout = QHBoxLayout()
        self.dominance_widget = QWidget()
        self.volume_widget = QWidget()
        row1_layout.addWidget(self.dominance_widget)
        row1_layout.addWidget(self.volume_widget)
        charts_layout.addLayout(row1_layout)
        
        # Deuxième ligne
        row2_layout = QHBoxLayout()
        self.marketcap_widget = QWidget()
        self.candlestick_widget = QWidget()
        row2_layout.addWidget(self.marketcap_widget)
        row2_layout.addWidget(self.candlestick_widget)
        charts_layout.addLayout(row2_layout)
        
        main_layout.addLayout(charts_layout)
        
        # Statut
        self.status_label = QLabel("Chargement des graphiques...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                color: #7c3aed;
                padding: 5px;
                background-color: #161b22;
                border-radius: 4px;
            }
        """)
        main_layout.addWidget(self.status_label)
    
    def load_charts(self):
        """Charge les graphiques avec les vraies données"""
        if not self.chart_generator:
            self.status_label.setText("❌ Générateur de graphiques non disponible")
            return
        
        try:
            # 1. Graphique de dominance avec vraies données
            self.load_dominance_chart()
            
            # 2. Graphique de volume avec vraies données
            self.load_volume_chart()
            
            # 3. Graphique de market cap avec vraies données
            self.load_marketcap_chart()
            
            # 4. Graphique candlestick avec vraies données
            self.load_candlestick_chart()
            
            self.status_label.setText("✅ Graphiques chargés avec les vraies données")
            
        except Exception as e:
            self.status_label.setText(f"❌ Erreur chargement: {e}")
            print(f"Erreur détaillée: {e}")
    
    def load_dominance_chart(self):
        """Charge le graphique de dominance"""
        try:
            # Le graphique va automatiquement récupérer les vraies données
            chart_widget = self.chart_generator.create_dominance_chart()
            
            # Remplacer le widget placeholder
            layout = QVBoxLayout(self.dominance_widget)
            layout.addWidget(QLabel("🔥 Dominance BTC/ETH/Alt (Vraies Données)"))
            layout.addWidget(chart_widget)
            
            print("✅ Graphique dominance chargé")
        except Exception as e:
            print(f"❌ Erreur dominance: {e}")
    
    def load_volume_chart(self):
        """Charge le graphique de volume"""
        try:
            chart_widget = self.chart_generator.create_volume_chart()
            
            layout = QVBoxLayout(self.volume_widget)
            layout.addWidget(QLabel("📊 Volume Global (Vraies Données)"))
            layout.addWidget(chart_widget)
            
            print("✅ Graphique volume chargé")
        except Exception as e:
            print(f"❌ Erreur volume: {e}")
    
    def load_marketcap_chart(self):
        """Charge le graphique de market cap"""
        try:
            chart_widget = self.chart_generator.create_marketcap_chart()
            
            layout = QVBoxLayout(self.marketcap_widget)
            layout.addWidget(QLabel("💰 Market Cap Global (Vraies Données)"))
            layout.addWidget(chart_widget)
            
            print("✅ Graphique market cap chargé")
        except Exception as e:
            print(f"❌ Erreur market cap: {e}")
    
    def load_candlestick_chart(self):
        """Charge le graphique candlestick"""
        try:
            chart_widget = self.chart_generator.create_candlestick_chart(symbol="BTC/USDT")
            
            layout = QVBoxLayout(self.candlestick_widget)
            layout.addWidget(QLabel("🕯️ BTC/USDT Candlestick (Vraies Données)"))
            layout.addWidget(chart_widget)
            
            print("✅ Graphique candlestick chargé")
        except Exception as e:
            print(f"❌ Erreur candlestick: {e}")

def test_data_availability():
    """Teste la disponibilité des données dans la base"""
    try:
        db = MarketDatabase()
        
        # Test données de marché
        market_data = db.get_market_data(limit=10)
        print(f"📊 Données de marché: {len(market_data)} entrées")
        
        if not market_data.empty:
            latest = market_data.iloc[-1]
            print(f"   - Dernière dominance BTC: {latest['btc_dom']:.2f}%")
            print(f"   - Volume global: ${latest['vol_global']/1e9:.1f}B")
            print(f"   - Market cap global: ${latest['mc_global']/1e12:.2f}T")
        
        # Test données OHLCV
        ohlcv_data = db.get_ohlcv("BTC/USDT", "binance", "1h", limit=10)
        print(f"🕯️ Données OHLCV BTC/USDT: {len(ohlcv_data)} entrées")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test données: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 Test des graphiques avec vraies données")
    print("=" * 50)
    
    # Test de disponibilité des données
    if not test_data_availability():
        print("⚠️ Pas de données disponibles, les graphiques utiliseront des données simulées")
    
    # Lancer l'application
    app = QApplication(sys.argv)
    
    # Style sombre
    app.setStyleSheet("""
        QMainWindow {
            background-color: #0d1117;
            color: #c9d1d9;
        }
        QWidget {
            background-color: #0d1117;
            color: #c9d1d9;
        }
        QLabel {
            color: #c9d1d9;
        }
    """)
    
    window = TestChartsWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
